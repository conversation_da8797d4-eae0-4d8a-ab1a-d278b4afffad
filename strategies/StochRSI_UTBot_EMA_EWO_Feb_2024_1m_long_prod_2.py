# OnlyHABot_RSI_EMA_ATR_15m

# pragma pylint: disable=missing-docstring, invalid-name, pointless-string-statement
# flake8: noqa: F401
# isort: skip_file
# --- Do not remove these libs ---
import json
import logging
import math
import time
import os
from functools import reduce
import numpy as np
import pandas as pd
from pandas import DataFrame
from datetime import timedelta, timezone, datetime
from typing import Optional, Union


from freqtrade.persistence import Trade

from freqtrade.exchange import timeframe_to_prev_date, timeframe_to_minutes
from freqtrade.rpc.api_server.api_schemas import SellReason
# import freqtrade.vendor.qtpylib.indicators as qtpylib

from freqtrade.strategy import (BooleanParameter, CategoricalParameter, stoploss_from_open, DecimalParameter,
                                IntParameter, IStrategy, informative, merge_informative_pair)

# --------------------------------
# Add your lib to import here
import talib.abstract as ta
import pandas_ta as pta
from technical import qtpylib

from freqtrade.strategy.interface import IStrategy

logger = logging.getLogger(__name__)

# custom indicators
# #############################################################################################################################################################################################

def stoch_rsi(dataframe, smoothK=3, smoothD=3, lengthRSI=14, lengthStoch=14, src='close'):
    """Calculates Stochastic RSI and adds K and D columns to the DataFrame.

    Args:
        data (pd.DataFrame): DataFrame containing price data.
        smoothK (int, optional): K smoothing period. Defaults to 3.
        smoothD (int, optional): D smoothing period. Defaults to 3.
        lengthRSI (int, optional): RSI calculation period. Defaults to 14.
        lengthStoch (int, optional): Stochastic calculation period. Defaults to 14.
        src (str, optional): Column name to use for RSI calculation. Defaults to 'close'.

    Returns:
        tuple: (pd.DataFrame, np.ndarray, np.ndarray)
            - DataFrame with added K and D columns
            - K values
            - D values
    """

    #RSI
    dataframe['rsi'] = ta.RSI(dataframe, timeperiod=lengthRSI)
    #StochRSI 

    stochrsi  = (dataframe['rsi'] - dataframe['rsi'].rolling(lengthStoch).min()) / (dataframe['rsi'].rolling(lengthStoch).max() - dataframe['rsi'].rolling(lengthStoch).min())
    dataframe['k'] = stochrsi.rolling(smoothK).mean() * 100
    dataframe['d'] = dataframe['k'].rolling(smoothD).mean()

    return dataframe

def calculate_heiken_ashi(dataframe):
    ha_open = (np.roll(dataframe['open'], 1) + np.roll(dataframe['close'], 1)) / 2
    ha_close = (dataframe['open'] + dataframe['high'] + dataframe['low'] + dataframe['close']) / 4
    ha_high = dataframe[['high', 'open', 'close']].max(axis=1)
    ha_low = dataframe[['low', 'open', 'close']].min(axis=1)
    
    ha_candles = pd.DataFrame({'open': ha_open, 'high': ha_high, 'low': ha_low, 'close': ha_close})
    return ha_candles


def UTBot_trend(dataframe, key_value=1, atr_period=3, ema_period=200):
    # Calculate ATR and xATRTrailingStop
    xATR = np.array(ta.ATR(dataframe['high'], dataframe['low'], dataframe['close'], timeperiod=atr_period))
    nLoss = key_value * xATR
    src = dataframe['close']

    # Initialize arrays
    xATRTrailingStop = np.zeros(len(dataframe))
    xATRTrailingStop[0] = src[0] - nLoss[0]

    # Calculate xATRTrailingStop using vectorized operations
    mask_1 = (src > np.roll(xATRTrailingStop, 1)) & (np.roll(src, 1) > np.roll(xATRTrailingStop, 1))
    mask_2 = (src < np.roll(xATRTrailingStop, 1)) & (np.roll(src, 1) < np.roll(xATRTrailingStop, 1))
    mask_3 = src > np.roll(xATRTrailingStop, 1)

    xATRTrailingStop = np.where(mask_1, np.maximum(np.roll(xATRTrailingStop, 1), src - nLoss), xATRTrailingStop)
    xATRTrailingStop = np.where(mask_2, np.minimum(np.roll(xATRTrailingStop, 1), src + nLoss), xATRTrailingStop)
    xATRTrailingStop = np.where(mask_3, src - nLoss, xATRTrailingStop)

    # Calculate pos using vectorized operations
    mask_buy = (np.roll(src, 1) < xATRTrailingStop) & (src > np.roll(xATRTrailingStop, 1))
    mask_sell = (np.roll(src, 1) > xATRTrailingStop)  & (src < np.roll(xATRTrailingStop, 1))

    pos = np.zeros(len(dataframe))
    pos = np.where(mask_buy, 1, pos)
    pos = np.where(mask_sell, -1, pos)
    pos[~((pos == 1) | (pos == -1))] = 0

    ema = np.array(ta.EMA(dataframe['close'], timeperiod=ema_period))

    buy_condition_utbot = (xATRTrailingStop > ema) & (pos > 0) & (src > ema)
    sell_condition_utbot = (xATRTrailingStop < ema) & (pos < 0) & (src < ema)    

    trend = np.where(buy_condition_utbot, 1, np.where(sell_condition_utbot, -1, 0))

    trend = np.array(trend)

    dataframe['trend'] = trend
    return dataframe


def ewo_signal(data, src='close', sma1length=5, sma2length=35, use_percent=True):
    # Function to calculate EWO
    def calculate_sma(data, length):
        return data[src].rolling(window=length).mean()

    # Calculate EWO
    sma1 = calculate_sma(data, sma1length)
    sma2 = calculate_sma(data, sma2length)

    if use_percent:
        ewo = (sma1 - sma2) / data[src] * 100
    else:
        ewo = sma1 - sma2

    # Determine EWO signals
    ewo_signal = np.where(ewo < 0, -1, 1)

    # Create a new DataFrame with only EWO_Signal column
    trend = np.array(ewo_signal)
    data['trend'] = trend

    return data

# Example usage:
# trend = calculate_trend(your_data_frame)
# print("Current Trend:", trend)



# ############################################################################################################################################################################################

class StochRSI_UTBot_EMA_EWO_Feb_2024_1m_long_prod_2(IStrategy):

    """
    Algorithm: StochRSI_UTBot_EMA_EWO_Feb_2024_1m_long_prod_2
    Author: Puranam Pradeep Picasso Sharma
    Contact: <EMAIL>
    GitHub: https://github.com/picasso999
    Patreon: https://patreon.com/pppicasso
    LinkedIn: https://www.linkedin.com/in/puranampradeeppicasso/
    Twitter: https://twitter.com/picasso_999

    Description:
    Feel free to contact me for further queries or collaboration opportunities.
    """

    
    # Strategy interface version - allow new iterations of the strategy interface.
    # Check the documentation or the Sample strategy to get the latest version.
    INTERFACE_VERSION = 3

    # Optimal timeframe for the strategy.
    timeframe = '1m'

    # Can this strategy go short?
    can_short = False



    '''

sudo docker compose run freqtrade backtesting --strategy StochRSI_UTBot_EMA_EWO_Feb_2024_1m_long_prod_2 -i 1m --export trades --breakdown month --timerange 20230101-20240118 --config user_data/config_StochRSI_UT_1m_long_2.json

2024-02-01 03:56:52,309 - freqtrade - INFO - freqtrade 2023.9
2024-02-01 03:56:52,325 - freqtrade.configuration.load_config - INFO - Using config: user_data/config_StochRSI_UT_1m_long_2.json ...
2024-02-01 03:56:52,326 - freqtrade.loggers - INFO - Verbosity set to 0
2024-02-01 03:56:52,326 - freqtrade.configuration.configuration - INFO - Parameter -i/--timeframe detected ... Using timeframe: 1m ...
2024-02-01 03:56:52,326 - freqtrade.configuration.configuration - INFO - Using max_open_trades: 4 ...
2024-02-01 03:56:52,326 - freqtrade.configuration.configuration - INFO - Parameter --timerange detected: 20230101-20240118 ...
2024-02-01 03:56:52,424 - freqtrade.configuration.configuration - INFO - Using user-data directory: /freqtrade/user_data ...
2024-02-01 03:56:52,425 - freqtrade.configuration.configuration - INFO - Using data directory: /freqtrade/user_data/data/binance ...
2024-02-01 03:56:52,425 - freqtrade.configuration.configuration - INFO - Overriding timeframe with Command line argument
2024-02-01 03:56:52,425 - freqtrade.configuration.configuration - INFO - Parameter --export detected: trades ...
2024-02-01 03:56:52,425 - freqtrade.configuration.configuration - INFO - Parameter --breakdown detected ...
2024-02-01 03:56:52,425 - freqtrade.configuration.configuration - INFO - Parameter --cache=day detected ...
2024-02-01 03:56:52,425 - freqtrade.configuration.configuration - INFO - Filter trades by timerange: 20230101-20240118
2024-02-01 03:56:52,427 - freqtrade.exchange.check_exchange - INFO - Checking exchange...
2024-02-01 03:56:52,449 - freqtrade.exchange.check_exchange - INFO - Exchange "binance" is officially supported by the Freqtrade development team.
2024-02-01 03:56:52,450 - freqtrade.configuration.configuration - INFO - Using pairlist from configuration.
2024-02-01 03:56:52,450 - freqtrade.configuration.config_validation - INFO - Validating configuration ...
2024-02-01 03:56:52,455 - freqtrade.commands.optimize_commands - INFO - Starting freqtrade in Backtesting mode
2024-02-01 03:56:52,456 - freqtrade.exchange.exchange - INFO - Instance is running with dry_run enabled
2024-02-01 03:56:52,456 - freqtrade.exchange.exchange - INFO - Using CCXT 4.0.105
2024-02-01 03:56:52,456 - freqtrade.exchange.exchange - INFO - Applying additional ccxt config: {'options': {'defaultType': 'swap'}, 'enableRateLimit': True}
2024-02-01 03:56:52,478 - freqtrade.exchange.exchange - INFO - Applying additional ccxt config: {'options': {'defaultType': 'swap'}, 'enableRateLimit': True, 'rateLimit': 50}
2024-02-01 03:56:52,497 - freqtrade.exchange.exchange - INFO - Using Exchange "Binance"
2024-02-01 03:56:56,447 - freqtrade.resolvers.exchange_resolver - INFO - Using resolved exchange 'Binance'...
2024-02-01 03:56:57,993 - freqtrade.resolvers.iresolver - INFO - Using resolved strategy StochRSI_UTBot_EMA_EWO_Feb_2024_1m_long_prod_2 from '/freqtrade/user_data/strategies/StochRSI_UTBot_EMA_EWO_Feb_2024_1m_long_prod_2.py'...
2024-02-01 03:56:57,993 - freqtrade.strategy.hyper - INFO - Found no parameter file.
2024-02-01 03:56:57,994 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'timeframe' with value in config file: 1m.
2024-02-01 03:56:57,994 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_currency' with value in config file: USDT.
2024-02-01 03:56:57,994 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_amount' with value in config file: 200.
2024-02-01 03:56:57,994 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'unfilledtimeout' with value in config file: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}.
2024-02-01 03:56:57,994 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'max_open_trades' with value in config file: 4.
2024-02-01 03:56:57,994 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using minimal_roi: {'0': 0.07899999999999999, '8': 0.040999999999999995, '20': 0.03, '50': 0.02, '200': 0.015, '350': 0.01, '600': 0.008, '900': 0.0065, '3080': 0}
2024-02-01 03:56:57,994 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using timeframe: 1m
2024-02-01 03:56:57,994 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stoploss: -0.265
2024-02-01 03:56:57,995 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop: True
2024-02-01 03:56:57,995 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop_positive: 0.005
2024-02-01 03:56:57,995 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop_positive_offset: 0.02
2024-02-01 03:56:57,995 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_only_offset_is_reached: True
2024-02-01 03:56:57,995 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_custom_stoploss: False
2024-02-01 03:56:57,995 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using process_only_new_candles: True
2024-02-01 03:56:57,995 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_types: {'entry': 'limit', 'exit': 'limit', 'stoploss': 'market', 'stoploss_on_exchange': False}
2024-02-01 03:56:57,995 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_time_in_force: {'entry': 'GTC', 'exit': 'GTC'}
2024-02-01 03:56:57,995 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_currency: USDT
2024-02-01 03:56:57,995 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_amount: 200
2024-02-01 03:56:57,995 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using protections: [{'method': 'CooldownPeriod', 'stop_duration_candles': 4}, {'method': 'MaxDrawdown', 'lookback_period_candles': 20, 'trade_limit': 1, 'stop_duration_candles': 300, 'max_allowed_drawdown': 0.1}, {'method': 'StoplossGuard', 'lookback_period_candles': 28, 'trade_limit': 1, 'stop_duration_candles': 300, 'only_per_pair': False}]
2024-02-01 03:56:57,996 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using startup_candle_count: 30
2024-02-01 03:56:57,996 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using unfilledtimeout: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}
2024-02-01 03:56:57,996 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_exit_signal: True
2024-02-01 03:56:57,996 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_only: False
2024-02-01 03:56:57,996 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_roi_if_entry_signal: False
2024-02-01 03:56:57,996 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_offset: 0.0
2024-02-01 03:56:57,996 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using disable_dataframe_checks: False
2024-02-01 03:56:57,996 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_buying_expired_candle_after: 0
2024-02-01 03:56:57,996 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using position_adjustment_enable: False
2024-02-01 03:56:57,996 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_entry_position_adjustment: -1
2024-02-01 03:56:57,996 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_open_trades: 4
2024-02-01 03:56:57,996 - freqtrade.configuration.config_validation - INFO - Validating configuration ...
2024-02-01 03:56:58,025 - freqtrade.resolvers.iresolver - INFO - Using resolved pairlist StaticPairList from '/freqtrade/freqtrade/plugins/pairlist/StaticPairList.py'...
2024-02-01 03:56:58,072 - freqtrade.resolvers.iresolver - INFO - Using resolved pairlist AgeFilter from '/freqtrade/freqtrade/plugins/pairlist/AgeFilter.py'...
2024-02-01 03:56:58,092 - freqtrade.resolvers.iresolver - INFO - Using resolved pairlist ShuffleFilter from '/freqtrade/freqtrade/plugins/pairlist/ShuffleFilter.py'...
2024-02-01 03:56:58,093 - ShuffleFilter - INFO - Backtesting mode detected, applying seed value: None
2024-02-01 03:57:02,547 - freqtrade.data.history.history_utils - INFO - Using indicator startup period: 30 ...
2024-02-01 03:57:11,311 - freqtrade.optimize.backtesting - INFO - Loading data from 2022-12-31 23:30:00 up to 2024-01-18 00:00:00 (382 days).
2024-02-01 03:57:12,009 - freqtrade.optimize.backtesting - INFO - Dataload complete. Calculating indicators
2024-02-01 03:57:12,188 - freqtrade.optimize.backtesting - INFO - Running backtesting for Strategy StochRSI_UTBot_EMA_EWO_Feb_2024_1m_long_prod_2
2024-02-01 03:57:12,188 - freqtrade.strategy.hyper - INFO - Strategy Parameter: adx_long_min = 30.8
2024-02-01 03:57:12,189 - freqtrade.strategy.hyper - INFO - Strategy Parameter: atr_period_l = 8
2024-02-01 03:57:12,189 - freqtrade.strategy.hyper - INFO - Strategy Parameter: df24h_val = 432
2024-02-01 03:57:12,189 - freqtrade.strategy.hyper - INFO - Strategy Parameter: df36h_val = 288
2024-02-01 03:57:12,189 - freqtrade.strategy.hyper - INFO - Strategy Parameter: ema_period_l = 68
2024-02-01 03:57:12,189 - freqtrade.strategy.hyper - INFO - Strategy Parameter: key_value_l = 2
2024-02-01 03:57:12,189 - freqtrade.strategy.hyper - INFO - Strategy Parameter: lengthRSI_l = 12
2024-02-01 03:57:12,189 - freqtrade.strategy.hyper - INFO - Strategy Parameter: leverage_num = 5
2024-02-01 03:57:12,189 - freqtrade.strategy.hyper - INFO - Strategy Parameter: sma1length_l = 5
2024-02-01 03:57:12,189 - freqtrade.strategy.hyper - INFO - Strategy Parameter: sma2length_l = 35
2024-02-01 03:57:12,190 - freqtrade.strategy.hyper - INFO - Strategy Parameter: volume_check = 26
2024-02-01 03:57:12,190 - freqtrade.strategy.hyper - INFO - Strategy Parameter: long_mul_sl = 6.5
2024-02-01 03:57:12,190 - freqtrade.strategy.hyper - INFO - Strategy Parameter: long_mul_tp = 6.3
2024-02-01 03:57:12,191 - freqtrade.strategy.hyper - INFO - Strategy Parameter: cooldown_lookback = 4
2024-02-01 03:57:12,191 - freqtrade.strategy.hyper - INFO - Strategy Parameter: max_allowed_drawdown = 0.1
2024-02-01 03:57:12,191 - freqtrade.strategy.hyper - INFO - Strategy Parameter: max_drawdown_lookback = 20
2024-02-01 03:57:12,191 - freqtrade.strategy.hyper - INFO - Strategy Parameter: max_drawdown_stop_duration = 300
2024-02-01 03:57:12,191 - freqtrade.strategy.hyper - INFO - Strategy Parameter: max_drawdown_trade_limit = 1
2024-02-01 03:57:12,191 - freqtrade.strategy.hyper - INFO - Strategy Parameter: stoploss_guard_lookback = 28
2024-02-01 03:57:12,191 - freqtrade.strategy.hyper - INFO - Strategy Parameter: stoploss_guard_stop_duration = 300
2024-02-01 03:57:12,191 - freqtrade.strategy.hyper - INFO - Strategy Parameter: stoploss_guard_trade_limit = 1
2024-02-01 03:57:41,153 - freqtrade.optimize.backtesting - INFO - Backtesting with data from 2023-01-01 00:00:00 up to 2024-01-18 00:00:00 (382 days).
(base) picasso999@picasso999-Inspiron-7570:~/Documents/FreqTradeAug15_2023/ft_userdata$ sudo docker compose run freqtrade backtesting --strategy StochRSI_UTBot_EMA_EWO_Feb_2024_1m_long_prod_2 -i 1m --export trades --breakdown month --timerange 20230601-20240118 --config user_data/config_StochRSI_UT_1m_long_2.json
2024-02-01 03:58:59,918 - freqtrade - INFO - freqtrade 2023.9
2024-02-01 03:58:59,943 - freqtrade.configuration.load_config - INFO - Using config: user_data/config_StochRSI_UT_1m_long_2.json ...
2024-02-01 03:58:59,946 - freqtrade.loggers - INFO - Verbosity set to 0
2024-02-01 03:58:59,946 - freqtrade.configuration.configuration - INFO - Parameter -i/--timeframe detected ... Using timeframe: 1m ...
2024-02-01 03:58:59,946 - freqtrade.configuration.configuration - INFO - Using max_open_trades: 4 ...
2024-02-01 03:58:59,946 - freqtrade.configuration.configuration - INFO - Parameter --timerange detected: 20230601-20240118 ...
2024-02-01 03:59:00,099 - freqtrade.configuration.configuration - INFO - Using user-data directory: /freqtrade/user_data ...
2024-02-01 03:59:00,100 - freqtrade.configuration.configuration - INFO - Using data directory: /freqtrade/user_data/data/binance ...
2024-02-01 03:59:00,100 - freqtrade.configuration.configuration - INFO - Overriding timeframe with Command line argument
2024-02-01 03:59:00,100 - freqtrade.configuration.configuration - INFO - Parameter --export detected: trades ...
2024-02-01 03:59:00,101 - freqtrade.configuration.configuration - INFO - Parameter --breakdown detected ...
2024-02-01 03:59:00,101 - freqtrade.configuration.configuration - INFO - Parameter --cache=day detected ...
2024-02-01 03:59:00,101 - freqtrade.configuration.configuration - INFO - Filter trades by timerange: 20230601-20240118
2024-02-01 03:59:00,103 - freqtrade.exchange.check_exchange - INFO - Checking exchange...
2024-02-01 03:59:00,113 - freqtrade.exchange.check_exchange - INFO - Exchange "binance" is officially supported by the Freqtrade development team.
2024-02-01 03:59:00,114 - freqtrade.configuration.configuration - INFO - Using pairlist from configuration.
2024-02-01 03:59:00,114 - freqtrade.configuration.config_validation - INFO - Validating configuration ...
2024-02-01 03:59:00,120 - freqtrade.commands.optimize_commands - INFO - Starting freqtrade in Backtesting mode
2024-02-01 03:59:00,120 - freqtrade.exchange.exchange - INFO - Instance is running with dry_run enabled
2024-02-01 03:59:00,120 - freqtrade.exchange.exchange - INFO - Using CCXT 4.0.105
2024-02-01 03:59:00,121 - freqtrade.exchange.exchange - INFO - Applying additional ccxt config: {'options': {'defaultType': 'swap'}, 'enableRateLimit': True}
2024-02-01 03:59:00,134 - freqtrade.exchange.exchange - INFO - Applying additional ccxt config: {'options': {'defaultType': 'swap'}, 'enableRateLimit': True, 'rateLimit': 50}
2024-02-01 03:59:00,144 - freqtrade.exchange.exchange - INFO - Using Exchange "Binance"
2024-02-01 03:59:03,933 - freqtrade.resolvers.exchange_resolver - INFO - Using resolved exchange 'Binance'...
2024-02-01 03:59:05,472 - freqtrade.resolvers.iresolver - INFO - Using resolved strategy StochRSI_UTBot_EMA_EWO_Feb_2024_1m_long_prod_2 from '/freqtrade/user_data/strategies/StochRSI_UTBot_EMA_EWO_Feb_2024_1m_long_prod_2.py'...
2024-02-01 03:59:05,473 - freqtrade.strategy.hyper - INFO - Found no parameter file.
2024-02-01 03:59:05,473 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'timeframe' with value in config file: 1m.
2024-02-01 03:59:05,473 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_currency' with value in config file: USDT.
2024-02-01 03:59:05,473 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_amount' with value in config file: 200.
2024-02-01 03:59:05,473 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'unfilledtimeout' with value in config file: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}.
2024-02-01 03:59:05,474 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'max_open_trades' with value in config file: 4.
2024-02-01 03:59:05,474 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using minimal_roi: {'0': 0.07899999999999999, '8': 0.040999999999999995, '20': 0.03, '50': 0.02, '200': 0.015, '350': 0.01, '600': 0.008, '900': 0.0065, '3080': 0}
2024-02-01 03:59:05,474 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using timeframe: 1m
2024-02-01 03:59:05,474 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stoploss: -0.265
2024-02-01 03:59:05,474 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop: True
2024-02-01 03:59:05,474 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop_positive: 0.005
2024-02-01 03:59:05,474 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop_positive_offset: 0.02
2024-02-01 03:59:05,474 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_only_offset_is_reached: True
2024-02-01 03:59:05,474 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_custom_stoploss: False
2024-02-01 03:59:05,474 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using process_only_new_candles: True
2024-02-01 03:59:05,474 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_types: {'entry': 'limit', 'exit': 'limit', 'stoploss': 'market', 'stoploss_on_exchange': False}
2024-02-01 03:59:05,474 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_time_in_force: {'entry': 'GTC', 'exit': 'GTC'}
2024-02-01 03:59:05,475 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_currency: USDT
2024-02-01 03:59:05,475 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_amount: 200
2024-02-01 03:59:05,475 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using protections: [{'method': 'CooldownPeriod', 'stop_duration_candles': 4}, {'method': 'MaxDrawdown', 'lookback_period_candles': 20, 'trade_limit': 1, 'stop_duration_candles': 300, 'max_allowed_drawdown': 0.1}, {'method': 'StoplossGuard', 'lookback_period_candles': 28, 'trade_limit': 1, 'stop_duration_candles': 300, 'only_per_pair': False}]
2024-02-01 03:59:05,475 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using startup_candle_count: 30
2024-02-01 03:59:05,475 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using unfilledtimeout: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}
2024-02-01 03:59:05,475 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_exit_signal: True
2024-02-01 03:59:05,475 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_only: False
2024-02-01 03:59:05,475 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_roi_if_entry_signal: False
2024-02-01 03:59:05,475 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_offset: 0.0
2024-02-01 03:59:05,475 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using disable_dataframe_checks: False
2024-02-01 03:59:05,475 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_buying_expired_candle_after: 0
2024-02-01 03:59:05,475 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using position_adjustment_enable: False
2024-02-01 03:59:05,476 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_entry_position_adjustment: -1
2024-02-01 03:59:05,476 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_open_trades: 4
2024-02-01 03:59:05,476 - freqtrade.configuration.config_validation - INFO - Validating configuration ...
2024-02-01 03:59:05,499 - freqtrade.resolvers.iresolver - INFO - Using resolved pairlist StaticPairList from '/freqtrade/freqtrade/plugins/pairlist/StaticPairList.py'...
2024-02-01 03:59:05,538 - freqtrade.resolvers.iresolver - INFO - Using resolved pairlist AgeFilter from '/freqtrade/freqtrade/plugins/pairlist/AgeFilter.py'...
2024-02-01 03:59:05,553 - freqtrade.resolvers.iresolver - INFO - Using resolved pairlist ShuffleFilter from '/freqtrade/freqtrade/plugins/pairlist/ShuffleFilter.py'...
2024-02-01 03:59:05,553 - ShuffleFilter - INFO - Backtesting mode detected, applying seed value: None
2024-02-01 03:59:09,954 - freqtrade.data.history.history_utils - INFO - Using indicator startup period: 30 ...
2024-02-01 03:59:16,379 - freqtrade.optimize.backtesting - INFO - Loading data from 2023-05-31 23:30:00 up to 2024-01-18 00:00:00 (231 days).
2024-02-01 03:59:17,018 - freqtrade.optimize.backtesting - INFO - Dataload complete. Calculating indicators
2024-02-01 03:59:17,276 - freqtrade.optimize.backtesting - INFO - Running backtesting for Strategy StochRSI_UTBot_EMA_EWO_Feb_2024_1m_long_prod_2
2024-02-01 03:59:17,276 - freqtrade.strategy.hyper - INFO - Strategy Parameter: adx_long_min = 30.8
2024-02-01 03:59:17,276 - freqtrade.strategy.hyper - INFO - Strategy Parameter: atr_period_l = 8
2024-02-01 03:59:17,276 - freqtrade.strategy.hyper - INFO - Strategy Parameter: df24h_val = 432
2024-02-01 03:59:17,276 - freqtrade.strategy.hyper - INFO - Strategy Parameter: df36h_val = 288
2024-02-01 03:59:17,276 - freqtrade.strategy.hyper - INFO - Strategy Parameter: ema_period_l = 68
2024-02-01 03:59:17,276 - freqtrade.strategy.hyper - INFO - Strategy Parameter: key_value_l = 2
2024-02-01 03:59:17,277 - freqtrade.strategy.hyper - INFO - Strategy Parameter: lengthRSI_l = 12
2024-02-01 03:59:17,277 - freqtrade.strategy.hyper - INFO - Strategy Parameter: leverage_num = 5
2024-02-01 03:59:17,277 - freqtrade.strategy.hyper - INFO - Strategy Parameter: sma1length_l = 5
2024-02-01 03:59:17,277 - freqtrade.strategy.hyper - INFO - Strategy Parameter: sma2length_l = 35
2024-02-01 03:59:17,277 - freqtrade.strategy.hyper - INFO - Strategy Parameter: volume_check = 26
2024-02-01 03:59:17,277 - freqtrade.strategy.hyper - INFO - Strategy Parameter: long_mul_sl = 6.5
2024-02-01 03:59:17,277 - freqtrade.strategy.hyper - INFO - Strategy Parameter: long_mul_tp = 6.3
2024-02-01 03:59:17,278 - freqtrade.strategy.hyper - INFO - Strategy Parameter: cooldown_lookback = 4
2024-02-01 03:59:17,278 - freqtrade.strategy.hyper - INFO - Strategy Parameter: max_allowed_drawdown = 0.1
2024-02-01 03:59:17,278 - freqtrade.strategy.hyper - INFO - Strategy Parameter: max_drawdown_lookback = 20
2024-02-01 03:59:17,278 - freqtrade.strategy.hyper - INFO - Strategy Parameter: max_drawdown_stop_duration = 300
2024-02-01 03:59:17,278 - freqtrade.strategy.hyper - INFO - Strategy Parameter: max_drawdown_trade_limit = 1
2024-02-01 03:59:17,278 - freqtrade.strategy.hyper - INFO - Strategy Parameter: stoploss_guard_lookback = 28
2024-02-01 03:59:17,278 - freqtrade.strategy.hyper - INFO - Strategy Parameter: stoploss_guard_stop_duration = 300
2024-02-01 03:59:17,278 - freqtrade.strategy.hyper - INFO - Strategy Parameter: stoploss_guard_trade_limit = 1
2024-02-01 03:59:33,559 - freqtrade.optimize.backtesting - INFO - Backtesting with data from 2023-06-01 00:00:00 up to 2024-01-18 00:00:00 (231 days).
2024-02-01 04:02:54,706 - freqtrade.misc - INFO - dumping json to "/freqtrade/user_data/backtest_results/backtest-result-2024-02-01_04-02-54.meta.json"
2024-02-01 04:02:54,707 - freqtrade.misc - INFO - dumping json to "/freqtrade/user_data/backtest_results/backtest-result-2024-02-01_04-02-54.json"
2024-02-01 04:02:54,720 - freqtrade.misc - INFO - dumping json to "/freqtrade/user_data/backtest_results/.last_result.json"
Result for strategy StochRSI_UTBot_EMA_EWO_Feb_2024_1m_long_prod_2
=============================================================== BACKTESTING REPORT ==============================================================
|            Pair |   Entries |   Avg Profit % |   Cum Profit % |   Tot Profit USDT |   Tot Profit % |   Avg Duration |   Win  Draw  Loss  Win% |
|-----------------+-----------+----------------+----------------+-------------------+----------------+----------------+-------------------------|
| SUSHI/USDT:USDT |        22 |           2.02 |          44.49 |            88.954 |           8.90 |        4:48:00 |    22     0     0   100 |
|   SFP/USDT:USDT |        21 |           1.89 |          39.67 |            79.327 |           7.93 |        1:18:00 |    21     0     0   100 |
|  SAND/USDT:USDT |        23 |           1.72 |          39.64 |            79.270 |           7.93 |        2:53:00 |    22     0     1  95.7 |
|   LPT/USDT:USDT |        19 |           2.04 |          38.83 |            77.663 |           7.77 |        4:21:00 |    19     0     0   100 |
|  NEAR/USDT:USDT |        17 |           2.08 |          35.28 |            70.519 |           7.05 |        1:02:00 |    17     0     0   100 |
|  KAVA/USDT:USDT |        22 |           1.49 |          32.86 |            65.724 |           6.57 |        8:58:00 |    22     0     0   100 |
|   ATA/USDT:USDT |        16 |           1.94 |          31.08 |            62.178 |           6.22 |        2:28:00 |    16     0     0   100 |
|   YFI/USDT:USDT |        14 |           2.03 |          28.47 |            56.769 |           5.68 |        4:29:00 |    14     0     0   100 |
|   SNX/USDT:USDT |        15 |           1.86 |          27.97 |            55.951 |           5.60 |        1:31:00 |    15     0     0   100 |
|   DGB/USDT:USDT |        11 |           2.42 |          26.64 |            53.298 |           5.33 |        1:34:00 |    11     0     0   100 |
|   TRB/USDT:USDT |        11 |           2.33 |          25.63 |            50.990 |           5.10 |        1:57:00 |    11     0     0   100 |
|   ICX/USDT:USDT |        12 |           1.57 |          18.82 |            37.642 |           3.76 |        3:29:00 |    12     0     0   100 |
|  EGLD/USDT:USDT |        18 |           0.34 |           6.09 |            12.176 |           1.22 |        1:29:00 |    17     0     1  94.4 |
|  API3/USDT:USDT |        18 |           0.22 |           4.01 |             8.016 |           0.80 |        3:37:00 |    17     0     1  94.4 |
|   REN/USDT:USDT |        18 |           0.00 |           0.07 |             0.144 |           0.01 |        5:50:00 |    16     0     2  88.9 |
|  ROSE/USDT:USDT |        16 |          -0.05 |          -0.87 |            -1.742 |          -0.17 |        4:01:00 |    15     0     1  93.8 |
|           TOTAL |       273 |           1.46 |         398.68 |           796.879 |          79.69 |        3:32:00 |   267     0     6  97.8 |
======================================================= LEFT OPEN TRADES REPORT ========================================================
|   Pair |   Entries |   Avg Profit % |   Cum Profit % |   Tot Profit USDT |   Tot Profit % |   Avg Duration |   Win  Draw  Loss  Win% |
|--------+-----------+----------------+----------------+-------------------+----------------+----------------+-------------------------|
|  TOTAL |         0 |           0.00 |           0.00 |             0.000 |           0.00 |           0:00 |     0     0     0     0 |
=========================================================== ENTER TAG STATS ===========================================================
|   TAG |   Entries |   Avg Profit % |   Cum Profit % |   Tot Profit USDT |   Tot Profit % |   Avg Duration |   Win  Draw  Loss  Win% |
|-------+-----------+----------------+----------------+-------------------+----------------+----------------+-------------------------|
| buy_1 |       273 |           1.46 |         398.68 |           796.879 |          79.69 |        3:32:00 |   267     0     6  97.8 |
| TOTAL |       273 |           1.46 |         398.68 |           796.879 |          79.69 |        3:32:00 |   267     0     6  97.8 |
======================================================== EXIT REASON STATS =========================================================
|          Exit Reason |   Exits |   Win  Draws  Loss  Win% |   Avg Profit % |   Cum Profit % |   Tot Profit USDT |   Tot Profit % |
|----------------------+---------+--------------------------+----------------+----------------+-------------------+----------------|
|   trailing_stop_loss |     141 |    140     0     1  99.3 |           2.06 |         291    |           581.767 |          72.75 |
|                  roi |     124 |    123     0     1  99.2 |           1.69 |         209.07 |           417.783 |          52.27 |
| take_profit (buy_1 ) |       4 |      4     0     0   100 |           1.41 |           5.66 |            11.311 |           1.41 |
|            stop_loss |       4 |      0     0     4     0 |         -26.76 |        -107.05 |          -213.982 |         -26.76 |
======================= MONTH BREAKDOWN ========================
|      Month |   Tot Profit USDT |   Wins |   Draws |   Losses |
|------------+-------------------+--------+---------+----------|
| 30/06/2023 |           159.67  |     36 |       0 |        0 |
| 31/07/2023 |            93.357 |     26 |       0 |        0 |
| 31/08/2023 |             4.257 |     18 |       0 |        2 |
| 30/09/2023 |            87.701 |     26 |       0 |        0 |
| 31/10/2023 |           187.84  |     53 |       0 |        1 |
| 30/11/2023 |            52.632 |     39 |       0 |        2 |
| 31/12/2023 |           134.133 |     46 |       0 |        1 |
| 31/01/2024 |            77.288 |     23 |       0 |        0 |
=================== SUMMARY METRICS ====================
| Metric                      | Value                  |
|-----------------------------+------------------------|
| Backtesting from            | 2023-06-01 00:00:00    |
| Backtesting to              | 2024-01-18 00:00:00    |
| Max open trades             | 4                      |
|                             |                        |
| Total/Daily Avg Trades      | 273 / 1.18             |
| Starting balance            | 1000 USDT              |
| Final balance               | 1796.879 USDT          |
| Absolute profit             | 796.879 USDT           |
| Total profit %              | 79.69%                 |
| CAGR %                      | 152.44%                |
| Sortino                     | 2.70                   |
| Sharpe                      | 9.16                   |
| Calmar                      | 157.95                 |
| Profit factor               | 4.67                   |
| Expectancy (Ratio)          | 2.92 (0.08)            |
| Trades per day              | 1.18                   |
| Avg. daily profit %         | 0.34%                  |
| Avg. stake amount           | 199.863 USDT           |
| Total trade volume          | 54562.672 USDT         |
|                             |                        |
| Best Pair                   | SUSHI/USDT:USDT 44.49% |
| Worst Pair                  | ROSE/USDT:USDT -0.87%  |
| Best trade                  | LPT/USDT:USDT 9.30%    |
| Worst trade                 | ROSE/USDT:USDT -26.85% |
| Best day                    | 50.123 USDT            |
| Worst day                   | -53.717 USDT           |
| Days win/draw/lose          | 127 / 96 / 5           |
| Avg. Duration Winners       | 3:15:00                |
| Avg. Duration Loser         | 15:48:00               |
| Max Consecutive Wins / Loss | 64 / 1                 |
| Rejected Entry signals      | 0                      |
| Entry/Exit Timeouts         | 0 / 6                  |
|                             |                        |
| Min balance                 | 1004.034 USDT          |
| Max balance                 | 1796.879 USDT          |
| Max % of account underwater | 4.17%                  |
| Absolute Drawdown (Account) | 4.17%                  |
| Absolute Drawdown           | 53.717 USDT            |
| Drawdown high               | 287.344 USDT           |
| Drawdown low                | 233.627 USDT           |
| Drawdown Start              | 2023-08-13 18:45:00    |
| Drawdown End                | 2023-08-15 19:07:00    |
| Market change               | 111.47%                |
========================================================

Backtested 2023-06-01 00:00:00 -> 2024-01-18 00:00:00 | Max open trades : 4
========================================================================================== STRATEGY SUMMARY =========================================================================================
|                                       Strategy |   Entries |   Avg Profit % |   Cum Profit % |   Tot Profit USDT |   Tot Profit % |   Avg Duration |   Win  Draw  Loss  Win% |           Drawdown |
|------------------------------------------------+-----------+----------------+----------------+-------------------+----------------+----------------+-------------------------+--------------------|
| StochRSI_UTBot_EMA_EWO_Feb_2024_1m_long_prod_2 |       273 |           1.46 |         398.68 |           796.879 |          79.69 |        3:32:00 |   267     0     6  97.8 | 53.717 USDT  4.17% |
=====================================================================================================================================================================================================



    sudo docker compose run freqtrade backtesting --strategy StochRSI_UTBot_EMA_EWO_Feb_2024_1m_long_prod_2 -i 1m --export trades --breakdown month --timerange 20230101-20230601 -
-config user_data/config_StochRSI_UT_1m_long_2.json
2024-02-01 04:05:53,439 - freqtrade - INFO - freqtrade 2023.9
2024-02-01 04:05:53,462 - freqtrade.configuration.load_config - INFO - Using config: user_data/config_StochRSI_UT_1m_long_2.json ...
2024-02-01 04:05:53,464 - freqtrade.loggers - INFO - Verbosity set to 0
2024-02-01 04:05:53,464 - freqtrade.configuration.configuration - INFO - Parameter -i/--timeframe detected ... Using timeframe: 1m ...
2024-02-01 04:05:53,464 - freqtrade.configuration.configuration - INFO - Using max_open_trades: 4 ...
2024-02-01 04:05:53,464 - freqtrade.configuration.configuration - INFO - Parameter --timerange detected: 20230101-20230601 ...
2024-02-01 04:05:53,596 - freqtrade.configuration.configuration - INFO - Using user-data directory: /freqtrade/user_data ...
2024-02-01 04:05:53,597 - freqtrade.configuration.configuration - INFO - Using data directory: /freqtrade/user_data/data/binance ...
2024-02-01 04:05:53,597 - freqtrade.configuration.configuration - INFO - Overriding timeframe with Command line argument
2024-02-01 04:05:53,597 - freqtrade.configuration.configuration - INFO - Parameter --export detected: trades ...
2024-02-01 04:05:53,597 - freqtrade.configuration.configuration - INFO - Parameter --breakdown detected ...
2024-02-01 04:05:53,597 - freqtrade.configuration.configuration - INFO - Parameter --cache=day detected ...
2024-02-01 04:05:53,597 - freqtrade.configuration.configuration - INFO - Filter trades by timerange: 20230101-20230601
2024-02-01 04:05:53,599 - freqtrade.exchange.check_exchange - INFO - Checking exchange...
2024-02-01 04:05:53,622 - freqtrade.exchange.check_exchange - INFO - Exchange "binance" is officially supported by the Freqtrade development team.
2024-02-01 04:05:53,622 - freqtrade.configuration.configuration - INFO - Using pairlist from configuration.
2024-02-01 04:05:53,622 - freqtrade.configuration.config_validation - INFO - Validating configuration ...
2024-02-01 04:05:53,631 - freqtrade.commands.optimize_commands - INFO - Starting freqtrade in Backtesting mode
2024-02-01 04:05:53,631 - freqtrade.exchange.exchange - INFO - Instance is running with dry_run enabled
2024-02-01 04:05:53,632 - freqtrade.exchange.exchange - INFO - Using CCXT 4.0.105
2024-02-01 04:05:53,632 - freqtrade.exchange.exchange - INFO - Applying additional ccxt config: {'options': {'defaultType': 'swap'}, 'enableRateLimit': True}
2024-02-01 04:05:53,655 - freqtrade.exchange.exchange - INFO - Applying additional ccxt config: {'options': {'defaultType': 'swap'}, 'enableRateLimit': True, 'rateLimit': 50}
2024-02-01 04:05:53,680 - freqtrade.exchange.exchange - INFO - Using Exchange "Binance"
2024-02-01 04:05:57,878 - freqtrade.resolvers.exchange_resolver - INFO - Using resolved exchange 'Binance'...
2024-02-01 04:06:00,306 - freqtrade.resolvers.iresolver - INFO - Using resolved strategy StochRSI_UTBot_EMA_EWO_Feb_2024_1m_long_prod_2 from '/freqtrade/user_data/strategies/StochRSI_UTBot_EMA_EWO_Feb_2024_1m_long_prod_2.py'...
2024-02-01 04:06:00,306 - freqtrade.strategy.hyper - INFO - Found no parameter file.
2024-02-01 04:06:00,307 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'timeframe' with value in config file: 1m.
2024-02-01 04:06:00,307 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_currency' with value in config file: USDT.
2024-02-01 04:06:00,307 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_amount' with value in config file: 200.
2024-02-01 04:06:00,307 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'unfilledtimeout' with value in config file: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}.
2024-02-01 04:06:00,307 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'max_open_trades' with value in config file: 4.
2024-02-01 04:06:00,308 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using minimal_roi: {'0': 0.07899999999999999, '8': 0.040999999999999995, '20': 0.03, '50': 0.02, '200': 0.015, '350': 0.01, '600': 0.008, '900': 0.0065, '3080': 0}
2024-02-01 04:06:00,308 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using timeframe: 1m
2024-02-01 04:06:00,308 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stoploss: -0.265
2024-02-01 04:06:00,308 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop: True
2024-02-01 04:06:00,308 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop_positive: 0.005
2024-02-01 04:06:00,308 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop_positive_offset: 0.02
2024-02-01 04:06:00,308 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_only_offset_is_reached: True
2024-02-01 04:06:00,308 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_custom_stoploss: False
2024-02-01 04:06:00,309 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using process_only_new_candles: True
2024-02-01 04:06:00,309 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_types: {'entry': 'limit', 'exit': 'limit', 'stoploss': 'market', 'stoploss_on_exchange': False}
2024-02-01 04:06:00,309 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_time_in_force: {'entry': 'GTC', 'exit': 'GTC'}
2024-02-01 04:06:00,309 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_currency: USDT
2024-02-01 04:06:00,309 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_amount: 200
2024-02-01 04:06:00,309 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using protections: [{'method': 'CooldownPeriod', 'stop_duration_candles': 4}, {'method': 'MaxDrawdown', 'lookback_period_candles': 20, 'trade_limit': 1, 'stop_duration_candles': 300, 'max_allowed_drawdown': 0.1}, {'method': 'StoplossGuard', 'lookback_period_candles': 28, 'trade_limit': 1, 'stop_duration_candles': 300, 'only_per_pair': False}]
2024-02-01 04:06:00,309 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using startup_candle_count: 30
2024-02-01 04:06:00,309 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using unfilledtimeout: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}
2024-02-01 04:06:00,309 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_exit_signal: True
2024-02-01 04:06:00,310 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_only: False
2024-02-01 04:06:00,310 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_roi_if_entry_signal: False
2024-02-01 04:06:00,310 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_offset: 0.0
2024-02-01 04:06:00,310 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using disable_dataframe_checks: False
2024-02-01 04:06:00,310 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_buying_expired_candle_after: 0
2024-02-01 04:06:00,310 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using position_adjustment_enable: False
2024-02-01 04:06:00,310 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_entry_position_adjustment: -1
2024-02-01 04:06:00,310 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_open_trades: 4
2024-02-01 04:06:00,311 - freqtrade.configuration.config_validation - INFO - Validating configuration ...
2024-02-01 04:06:00,345 - freqtrade.resolvers.iresolver - INFO - Using resolved pairlist StaticPairList from '/freqtrade/freqtrade/plugins/pairlist/StaticPairList.py'...
2024-02-01 04:06:00,396 - freqtrade.resolvers.iresolver - INFO - Using resolved pairlist AgeFilter from '/freqtrade/freqtrade/plugins/pairlist/AgeFilter.py'...
2024-02-01 04:06:00,417 - freqtrade.resolvers.iresolver - INFO - Using resolved pairlist ShuffleFilter from '/freqtrade/freqtrade/plugins/pairlist/ShuffleFilter.py'...
2024-02-01 04:06:00,417 - ShuffleFilter - INFO - Backtesting mode detected, applying seed value: None
2024-02-01 04:06:04,807 - freqtrade.data.history.history_utils - INFO - Using indicator startup period: 30 ...
2024-02-01 04:06:12,085 - freqtrade.optimize.backtesting - INFO - Loading data from 2022-12-31 23:30:00 up to 2023-06-01 00:00:00 (151 days).
2024-02-01 04:06:12,959 - freqtrade.optimize.backtesting - INFO - Dataload complete. Calculating indicators
2024-02-01 04:06:13,062 - freqtrade.optimize.backtesting - INFO - Running backtesting for Strategy StochRSI_UTBot_EMA_EWO_Feb_2024_1m_long_prod_2
2024-02-01 04:06:13,062 - freqtrade.strategy.hyper - INFO - Strategy Parameter: adx_long_min = 30.8
2024-02-01 04:06:13,063 - freqtrade.strategy.hyper - INFO - Strategy Parameter: atr_period_l = 8
2024-02-01 04:06:13,063 - freqtrade.strategy.hyper - INFO - Strategy Parameter: df24h_val = 432
2024-02-01 04:06:13,063 - freqtrade.strategy.hyper - INFO - Strategy Parameter: df36h_val = 288
2024-02-01 04:06:13,063 - freqtrade.strategy.hyper - INFO - Strategy Parameter: ema_period_l = 68
2024-02-01 04:06:13,063 - freqtrade.strategy.hyper - INFO - Strategy Parameter: key_value_l = 2
2024-02-01 04:06:13,063 - freqtrade.strategy.hyper - INFO - Strategy Parameter: lengthRSI_l = 12
2024-02-01 04:06:13,064 - freqtrade.strategy.hyper - INFO - Strategy Parameter: leverage_num = 5
2024-02-01 04:06:13,064 - freqtrade.strategy.hyper - INFO - Strategy Parameter: sma1length_l = 5
2024-02-01 04:06:13,064 - freqtrade.strategy.hyper - INFO - Strategy Parameter: sma2length_l = 35
2024-02-01 04:06:13,064 - freqtrade.strategy.hyper - INFO - Strategy Parameter: volume_check = 26
2024-02-01 04:06:13,065 - freqtrade.strategy.hyper - INFO - Strategy Parameter: long_mul_sl = 6.5
2024-02-01 04:06:13,065 - freqtrade.strategy.hyper - INFO - Strategy Parameter: long_mul_tp = 6.3
2024-02-01 04:06:13,065 - freqtrade.strategy.hyper - INFO - Strategy Parameter: cooldown_lookback = 4
2024-02-01 04:06:13,065 - freqtrade.strategy.hyper - INFO - Strategy Parameter: max_allowed_drawdown = 0.1
2024-02-01 04:06:13,066 - freqtrade.strategy.hyper - INFO - Strategy Parameter: max_drawdown_lookback = 20
2024-02-01 04:06:13,066 - freqtrade.strategy.hyper - INFO - Strategy Parameter: max_drawdown_stop_duration = 300
2024-02-01 04:06:13,066 - freqtrade.strategy.hyper - INFO - Strategy Parameter: max_drawdown_trade_limit = 1
2024-02-01 04:06:13,066 - freqtrade.strategy.hyper - INFO - Strategy Parameter: stoploss_guard_lookback = 28
2024-02-01 04:06:13,066 - freqtrade.strategy.hyper - INFO - Strategy Parameter: stoploss_guard_stop_duration = 300
2024-02-01 04:06:13,066 - freqtrade.strategy.hyper - INFO - Strategy Parameter: stoploss_guard_trade_limit = 1
2024-02-01 04:06:28,580 - freqtrade.optimize.backtesting - INFO - Backtesting with data from 2023-01-01 00:00:00 up to 2023-06-01 00:00:00 (151 days).
2024-02-01 04:09:27,583 - freqtrade.misc - INFO - dumping json to "/freqtrade/user_data/backtest_results/backtest-result-2024-02-01_04-09-27.meta.json"
2024-02-01 04:09:27,583 - freqtrade.misc - INFO - dumping json to "/freqtrade/user_data/backtest_results/backtest-result-2024-02-01_04-09-27.json"
2024-02-01 04:09:27,595 - freqtrade.misc - INFO - dumping json to "/freqtrade/user_data/backtest_results/.last_result.json"
Result for strategy StochRSI_UTBot_EMA_EWO_Feb_2024_1m_long_prod_2
=============================================================== BACKTESTING REPORT ==============================================================
|            Pair |   Entries |   Avg Profit % |   Cum Profit % |   Tot Profit USDT |   Tot Profit % |   Avg Duration |   Win  Draw  Loss  Win% |
|-----------------+-----------+----------------+----------------+-------------------+----------------+----------------+-------------------------|
|   SNX/USDT:USDT |        15 |           2.30 |          34.45 |            68.897 |           6.89 |        2:23:00 |    15     0     0   100 |
|   REN/USDT:USDT |        16 |           2.12 |          33.90 |            67.816 |           6.78 |        1:51:00 |    16     0     0   100 |
|  NEAR/USDT:USDT |        14 |           2.22 |          31.08 |            62.106 |           6.21 |        1:00:00 |    14     0     0   100 |
|  EGLD/USDT:USDT |        17 |           1.78 |          30.22 |            60.328 |           6.03 |        3:17:00 |    17     0     0   100 |
|  SAND/USDT:USDT |        15 |           1.94 |          29.14 |            58.261 |           5.83 |        1:53:00 |    15     0     0   100 |
|   YFI/USDT:USDT |        14 |           1.90 |          26.60 |            52.959 |           5.30 |        4:15:00 |    14     0     0   100 |
|   SFP/USDT:USDT |        15 |           1.51 |          22.70 |            45.402 |           4.54 |        1:02:00 |    14     0     1  93.3 |
|   ATA/USDT:USDT |         9 |           2.29 |          20.59 |            41.184 |           4.12 |        0:58:00 |     9     0     0   100 |
|   ICX/USDT:USDT |        13 |           1.57 |          20.47 |            40.942 |           4.09 |        1:08:00 |    12     0     1  92.3 |
|  API3/USDT:USDT |         8 |           1.86 |          14.85 |            29.702 |           2.97 |        2:48:00 |     8     0     0   100 |
|   TRB/USDT:USDT |         9 |           1.65 |          14.85 |            29.694 |           2.97 |        4:02:00 |     9     0     0   100 |
| SUSHI/USDT:USDT |        10 |           1.39 |          13.86 |            27.695 |           2.77 |        0:53:00 |     9     0     1  90.0 |
|  KAVA/USDT:USDT |        25 |           0.50 |          12.60 |            25.209 |           2.52 |        3:37:00 |    22     0     3  88.0 |
|  ROSE/USDT:USDT |        22 |           0.47 |          10.29 |            20.576 |           2.06 |        2:42:00 |    20     0     2  90.9 |
|   LPT/USDT:USDT |        21 |           0.42 |           8.85 |            17.695 |           1.77 |        4:08:00 |    19     0     2  90.5 |
|   DGB/USDT:USDT |         6 |          -2.77 |         -16.64 |           -33.290 |          -3.33 |        2:27:00 |     5     0     1  83.3 |
|           TOTAL |       229 |           1.34 |         307.81 |           615.173 |          61.52 |        2:32:00 |   218     0    11  95.2 |
======================================================= LEFT OPEN TRADES REPORT ========================================================
|   Pair |   Entries |   Avg Profit % |   Cum Profit % |   Tot Profit USDT |   Tot Profit % |   Avg Duration |   Win  Draw  Loss  Win% |
|--------+-----------+----------------+----------------+-------------------+----------------+----------------+-------------------------|
|  TOTAL |         0 |           0.00 |           0.00 |             0.000 |           0.00 |           0:00 |     0     0     0     0 |
=========================================================== ENTER TAG STATS ===========================================================
|   TAG |   Entries |   Avg Profit % |   Cum Profit % |   Tot Profit USDT |   Tot Profit % |   Avg Duration |   Win  Draw  Loss  Win% |
|-------+-----------+----------------+----------------+-------------------+----------------+----------------+-------------------------|
| buy_1 |       229 |           1.34 |         307.81 |           615.173 |          61.52 |        2:32:00 |   218     0    11  95.2 |
| TOTAL |       229 |           1.34 |         307.81 |           615.173 |          61.52 |        2:32:00 |   218     0    11  95.2 |
======================================================== EXIT REASON STATS =========================================================
|          Exit Reason |   Exits |   Win  Draws  Loss  Win% |   Avg Profit % |   Cum Profit % |   Tot Profit USDT |   Tot Profit % |
|----------------------+---------+--------------------------+----------------+----------------+-------------------+----------------|
|   trailing_stop_loss |     114 |    107     0     7  93.9 |           1.82 |         207.92 |           415.585 |          51.98 |
|                  roi |     108 |    108     0     0   100 |           1.89 |         203.7  |           407.247 |          50.93 |
|            stop_loss |       4 |      0     0     4     0 |         -26.75 |        -106.98 |          -213.992 |         -26.75 |
| take_profit (buy_1 ) |       3 |      3     0     0   100 |           1.06 |           3.17 |             6.334 |           0.79 |
======================= MONTH BREAKDOWN ========================
|      Month |   Tot Profit USDT |   Wins |   Draws |   Losses |
|------------+-------------------+--------+---------+----------|
| 31/01/2023 |           197.049 |     68 |       0 |        4 |
| 28/02/2023 |            42.583 |     39 |       0 |        4 |
| 31/03/2023 |           165.991 |     41 |       0 |        1 |
| 30/04/2023 |           102.994 |     42 |       0 |        1 |
| 31/05/2023 |           106.556 |     28 |       0 |        1 |
=================== SUMMARY METRICS ====================
| Metric                      | Value                  |
|-----------------------------+------------------------|
| Backtesting from            | 2023-01-01 00:00:00    |
| Backtesting to              | 2023-06-01 00:00:00    |
| Max open trades             | 4                      |
|                             |                        |
| Total/Daily Avg Trades      | 229 / 1.52             |
| Starting balance            | 1000 USDT              |
| Final balance               | 1615.173 USDT          |
| Absolute profit             | 615.173 USDT           |
| Total profit %              | 61.52%                 |
| CAGR %                      | 218.65%                |
| Sortino                     | 3.37                   |
| Sharpe                      | 9.85                   |
| Calmar                      | 146.11                 |
| Profit factor               | 3.43                   |
| Expectancy (Ratio)          | 2.69 (0.12)            |
| Trades per day              | 1.52                   |
| Avg. daily profit %         | 0.41%                  |
| Avg. stake amount           | 199.87 USDT            |
| Total trade volume          | 45770.316 USDT         |
|                             |                        |
| Best Pair                   | SNX/USDT:USDT 34.45%   |
| Worst Pair                  | DGB/USDT:USDT -16.64%  |
| Best trade                  | SNX/USDT:USDT 9.30%    |
| Worst trade                 | KAVA/USDT:USDT -26.95% |
| Best day                    | 45.683 USDT            |
| Worst day                   | -53.473 USDT           |
| Days win/draw/lose          | 88 / 56 / 5            |
| Avg. Duration Winners       | 2:19:00                |
| Avg. Duration Loser         | 6:51:00                |
| Max Consecutive Wins / Loss | 54 / 1                 |
| Rejected Entry signals      | 0                      |
| Entry/Exit Timeouts         | 0 / 14                 |
|                             |                        |
| Min balance                 | 963.942 USDT           |
| Max balance                 | 1615.173 USDT          |
| Max % of account underwater | 5.33%                  |
| Absolute Drawdown (Account) | 5.33%                  |
| Absolute Drawdown           | 64.973 USDT            |
| Drawdown high               | 219.723 USDT           |
| Drawdown low                | 154.75 USDT            |
| Drawdown Start              | 2023-02-04 13:40:00    |
| Drawdown End                | 2023-02-16 22:31:00    |
| Market change               | 27.36%                 |
========================================================

Backtested 2023-01-01 00:00:00 -> 2023-06-01 00:00:00 | Max open trades : 4
========================================================================================== STRATEGY SUMMARY =========================================================================================
|                                       Strategy |   Entries |   Avg Profit % |   Cum Profit % |   Tot Profit USDT |   Tot Profit % |   Avg Duration |   Win  Draw  Loss  Win% |           Drawdown |
|------------------------------------------------+-----------+----------------+----------------+-------------------+----------------+----------------+-------------------------+--------------------|
| StochRSI_UTBot_EMA_EWO_Feb_2024_1m_long_prod_2 |       229 |           1.34 |         307.81 |           615.173 |          61.52 |        2:32:00 |   218     0    11  95.2 | 64.973 USDT  5.33% |
=====================================================================================================================================================================================================
    
        '''
    
    # Minimal ROI designed for the strategy.
    # minimal_roi = {"0": 0.09, "127": 0.07, "333": 0.04, "2038": 0}
    # minimal_roi = {"0": 0.06, "12": 0.05, "33": 0.04, "60": 0.03, "150": 0.02, "200": 0.01, "500": 0.007,"800": 0.006 ,"1080": 0} #1m tf adn 1x leverage setup
    minimal_roi = {"0": 0.07899999999999999, "8": 0.040999999999999995, "20": 0.03,"50": 0.02, "200": 0.015, "350": 0.01,"600": 0.008, "900" : 0.0065 ,"3080": 0} #1m tf adn 1x leverage setup
    # minimal_roi = {"0": 0.06, "12": 0.05, "33": 0.04, "60": 0.03, "150": 0.02, "200": 0.015, "500": 0.01,"800": 0.008, "1100" : 0.0065 ,"3080": 0} #1m tf adn 2x leverage setup
    
    # minimal_roi = {"0": 0.09, "12": 0.07, "33": 0.04, "60": 0.02, "150": 0.01, "1080": 0}

    # Optimal stoploss designed for the strategy.
    # This attribute will be overridden if the config file contains "stoploss".
    stoploss = -0.265 #19% gave good return for 4 motnhs test 
    # use_custom_stoploss = True

    # Trailing stop:
    trailing_stop = True
    trailing_stop_positive = 0.005
    trailing_stop_positive_offset = 0.02
    trailing_only_offset_is_reached = True


    buy_params = {
      "adx_long_min": 30.8,
      "atr_period_l": 8,
      "df24h_val": 432,
      "df36h_val": 288,
      "ema_period_l": 68,
      "key_value_l": 2,
      "lengthRSI_l": 12,
      "leverage_num": 5, # lev 5 gives best result
      "sma1length_l": 5,
      "sma2length_l": 35,
      "volume_check": 26
    }
    sell_params = {
      "long_mul_sl": 6.5,
      "long_mul_tp": 6.3
    }
    # protection_params = {
    #   "cooldown_lookback": 40,
    #   "max_allowed_drawdown": 0.1,
    #   "max_drawdown_lookback": 20,
    #   "max_drawdown_stop_duration": 300,
    #   "max_drawdown_trade_limit": 1,
    #   "stoploss_guard_lookback": 28,
    #   "stoploss_guard_stop_duration": 300,
    #   "stoploss_guard_trade_limit": 1
    # }


    # Run "populate_indicators()" only for new candle.
    process_only_new_candles = True

    # These values can be overridden in the config.
    use_exit_signal = True
    exit_profit_only = False
    ignore_roi_if_entry_signal = False

    # Number of candles the strategy requires before producing valid signals
    startup_candle_count: int = 30

    #leverage here
    leverage_optimize = False
    leverage_num = IntParameter(low=1, high=5, default=buy_params['leverage_num'], space='buy', optimize=leverage_optimize)

    # Strategy parameters
    parameters_yes = True
    parameters_no = False

    '''

    '''

    lowest_prices = {}
    highest_prices = {}
    price_drop_percentage = {}
    pairs_close_to_high = []
    pairs_close_to_low = []
    locked = []
    inf_1h = '1h'



    key_value_l = IntParameter(1, 25, default=int(buy_params["key_value_l"]), space='buy', optimize= parameters_yes)
    atr_period_l = IntParameter(1, 25, default=int(buy_params["atr_period_l"]), space='buy', optimize= parameters_yes)
    ema_period_l = IntParameter(10, 250, default=int(buy_params["ema_period_l"]), space='buy', optimize= parameters_yes)
        

    sma1length_l = IntParameter(2, 15, default=int(buy_params["sma1length_l"]), space='buy', optimize= parameters_no)
    sma2length_l = IntParameter(15, 40, default=int(buy_params["sma2length_l"]), space='buy', optimize= parameters_no)



    lengthRSI_l = IntParameter(5, 25, default=int(buy_params["lengthRSI_l"]), space='buy', optimize= parameters_yes)

    adx_long_min = DecimalParameter(19.5, 60.0, default=int(buy_params["adx_long_min"]), decimals = 1, space="buy", optimize = parameters_yes)


    volume_check = IntParameter(5, 45, default=int(buy_params["volume_check"]), space='buy', optimize= parameters_yes)

    df24h_val =  IntParameter(1, 245, default=int(buy_params["df24h_val"]), space='buy', optimize= parameters_no)
    df36h_val =  IntParameter(1, 245, default=int(buy_params["df36h_val"]), space='buy', optimize= parameters_no)


    
    long_mul_tp = DecimalParameter(2.0, 20.5, default=float(sell_params["long_mul_tp"]), decimals = 1, space='sell', optimize = parameters_yes)

    long_mul_sl = DecimalParameter(2.0, 20.5, default=float(sell_params["long_mul_sl"]), decimals = 1, space='sell', optimize = parameters_yes)

    # protect_optimize = True
    # cooldown_lookback = IntParameter(1, 40, default=40, space="protection", optimize=protect_optimize)
    # max_drawdown_lookback = IntParameter(1, 50, default=int(protection_params["max_drawdown_lookback"]), space="protection", optimize=protect_optimize)
    # max_drawdown_trade_limit = IntParameter(1, 3, default=int(protection_params["max_drawdown_trade_limit"]), space="protection", optimize=protect_optimize)
    # max_drawdown_stop_duration = IntParameter(1, 50, default=int(protection_params["max_drawdown_stop_duration"]), space="protection", optimize=protect_optimize)
    # max_allowed_drawdown = DecimalParameter(0.05, 0.30, default=float(protection_params["max_allowed_drawdown"]), decimals=2, space="protection",
    #                                         optimize=protect_optimize)
    # stoploss_guard_lookback = IntParameter(1, 50, default=int(protection_params["stoploss_guard_lookback"]), space="protection", optimize=protect_optimize)
    # stoploss_guard_trade_limit = IntParameter(1, 3, default=int(protection_params["stoploss_guard_trade_limit"]), space="protection", optimize=protect_optimize)
    # stoploss_guard_stop_duration = IntParameter(1, 50, default=int(protection_params["stoploss_guard_stop_duration"]), space="protection", optimize=protect_optimize)

    # @property
    # def protections(self):
    #     return [
    #         {
    #             "method": "CooldownPeriod",
    #             "stop_duration_candles": self.cooldown_lookback.value
    #         },
    #         {
    #             "method": "MaxDrawdown",
    #             "lookback_period_candles": self.max_drawdown_lookback.value,
    #             "trade_limit": self.max_drawdown_trade_limit.value,
    #             "stop_duration_candles": self.max_drawdown_stop_duration.value,
    #             "max_allowed_drawdown": self.max_allowed_drawdown.value
    #         },
    #         {
    #             "method": "StoplossGuard",
    #             "lookback_period_candles": self.stoploss_guard_lookback.value,
    #             "trade_limit": self.stoploss_guard_trade_limit.value,
    #             "stop_duration_candles": self.stoploss_guard_stop_duration.value,
    #             "only_per_pair": False
    #         }
    #     ]


    # Optional order type mapping.
    order_types = {
        'entry': 'limit',
        'exit': 'limit',
        'stoploss': 'market',
        'stoploss_on_exchange': False
    }

    # Optional order time in force.
    order_time_in_force = {
        'entry': 'GTC',
        'exit': 'GTC'
    }
    
    @property
    def plot_config(self):
        return {
            # Main plot indicators (Moving averages, ...)
            'main_plot': {
                'tema': {},
                'sar': {'color': 'white'},
            },
            'subplots': {
                # Subplots - each dict defines one additional plot
                "MACD": {
                    'macd': {'color': 'blue'},
                    'macdsignal': {'color': 'orange'},
                },
                "RSI": {
                    'rsi': {'color': 'red'},
                }
            }
        }
    
    def pump_dump_protection(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        df36h = dataframe.copy().shift(self.df36h_val.value) # 432 mins , 86 for 5m, 29 for 15m
        df24h = dataframe.copy().shift(self.df24h_val.value) # 288 mins, 58 for 5m , 20 for 15m
        dataframe['volume_mean_short'] = dataframe['volume'].rolling(4).mean() # 4 rolling candles, 4 for 1m & 3m, 2 for 5m , 1 for 15m 
        dataframe['volume_mean_long'] = df24h['volume'].rolling(48).mean() # 48 rolling candles, 10 for 5m, 16 for 3m, 4 for 15m
        dataframe['volume_mean_base'] = df36h['volume'].rolling(238).mean() # 238 rolling candles, 48 for 15m, 96 for 3m, 16 for 15m
        dataframe['pnd_volume_warn'] = np.where((dataframe['volume_mean_short'] / dataframe['volume_mean_long'] > 5.0), -1, 1)
        return dataframe

 
    def informative_pairs(self):
        
        return [
            ('BTC/USDT:USDT', '15m'),
            ('ETH/USDT:USDT', '15m'),
            ('BTC/USDT:USDT', '1h'),
            ('ETH/USDT:USDT', '1h')
        ]
    
        
    @informative('1h', 'BTC/{stake}:{stake}', '{base}_{column}_{timeframe}')
    def populate_indicators_btc_1h(self, dataframe: DataFrame, metadata: dict) -> DataFrame:

        L_UTBot_trend_alert_btc_1h = UTBot_trend(dataframe=dataframe, key_value=self.key_value_l.value, atr_period=self.atr_period_l.value, ema_period=self.ema_period_l.value)
        dataframe['UT_Trend_L'] = L_UTBot_trend_alert_btc_1h['trend']

        dataframe['ema'] = ta.EMA(dataframe['close'], timeperiod=55)
        dataframe['ema_22'] = ta.EMA(dataframe['close'], timeperiod=22)
        dataframe['rsi'] =  ta.RSI(dataframe, timeperiod=8)
        dataframe['closeBTC'] = dataframe['close']
        # Stochastic Fast
        stoch_fast = ta.STOCHF(dataframe)
        dataframe['fastd'] = stoch_fast['fastd']
        dataframe['fastk'] = stoch_fast['fastk']
        return dataframe
    
    @informative('1h', 'ETH/{stake}:{stake}', '{base}_{column}_{timeframe}')
    def populate_indicators_eth_1h(self, dataframe: DataFrame, metadata: dict) -> DataFrame:

        L_UTBot_trend_alert_ETH_1h = UTBot_trend(dataframe=dataframe, key_value=self.key_value_l.value, atr_period=self.atr_period_l.value, ema_period=self.ema_period_l.value)
        dataframe['UT_Trend_L'] = L_UTBot_trend_alert_ETH_1h['trend']

        dataframe['ema'] = ta.EMA(dataframe['close'], timeperiod=55)
        dataframe['ema_22'] = ta.EMA(dataframe['close'], timeperiod=22)
        dataframe['rsi'] =  ta.RSI(dataframe, timeperiod=8)
        dataframe['closeETH'] = dataframe['close']
        # Stochastic Fast
        stoch_fast = ta.STOCHF(dataframe)
        dataframe['fastd'] = stoch_fast['fastd']
        dataframe['fastk'] = stoch_fast['fastk']
        return dataframe
    
    @informative('15m', 'BTC/{stake}:{stake}', '{base}_{column}_{timeframe}')
    def populate_indicators_btc_15m(self, dataframe: DataFrame, metadata: dict) -> DataFrame:

        L_UTBot_trend_alert_btc_15m = UTBot_trend(dataframe=dataframe, key_value=self.key_value_l.value, atr_period=self.atr_period_l.value, ema_period=self.ema_period_l.value)
        dataframe['UT_Trend_L'] = L_UTBot_trend_alert_btc_15m['trend']

        dataframe['ema'] = ta.EMA(dataframe['close'], timeperiod=55)
        dataframe['ema_22'] = ta.EMA(dataframe['close'], timeperiod=22)
        dataframe['rsi'] =  ta.RSI(dataframe, timeperiod=8)
        dataframe['closeBTC'] = dataframe['close']
        # Stochastic Fast
        stoch_fast = ta.STOCHF(dataframe)
        dataframe['fastd'] = stoch_fast['fastd']
        dataframe['fastk'] = stoch_fast['fastk']
        return dataframe
    
    @informative('15m', 'ETH/{stake}:{stake}', '{base}_{column}_{timeframe}')
    def populate_indicators_eth_15m(self, dataframe: DataFrame, metadata: dict) -> DataFrame:

        L_UTBot_trend_alert_ETH_15m = UTBot_trend(dataframe=dataframe, key_value=self.key_value_l.value, atr_period=self.atr_period_l.value, ema_period=self.ema_period_l.value)
        dataframe['UT_Trend_L'] = L_UTBot_trend_alert_ETH_15m['trend']

        dataframe['ema'] = ta.EMA(dataframe['close'], timeperiod=55)
        dataframe['ema_22'] = ta.EMA(dataframe['close'], timeperiod=22)
        dataframe['rsi'] =  ta.RSI(dataframe, timeperiod=8)
        dataframe['closeETH'] = dataframe['close']
        # Stochastic Fast
        stoch_fast = ta.STOCHF(dataframe)
        dataframe['fastd'] = stoch_fast['fastd']
        dataframe['fastk'] = stoch_fast['fastk']
        return dataframe

    @informative('15m')
    def populate_indicators_15m(self, dataframe: DataFrame, metadata: dict) -> DataFrame:

        L_UTBot_trend_alert_self_15m = UTBot_trend(dataframe=dataframe, key_value=self.key_value_l.value, atr_period=self.atr_period_l.value, ema_period=self.ema_period_l.value)
        dataframe['UT_Trend_L'] = L_UTBot_trend_alert_self_15m['trend']

        dataframe['ema'] = ta.EMA(dataframe['close'], timeperiod=55)
        dataframe['ema_22'] = ta.EMA(dataframe['close'], timeperiod=22)
        dataframe['rsi'] =  ta.RSI(dataframe, timeperiod=8)
        # Stochastic Fast
        stoch_fast = ta.STOCHF(dataframe)
        dataframe['fastd'] = stoch_fast['fastd']
        dataframe['fastk'] = stoch_fast['fastk']

        return dataframe
    
    @informative('1h')
    def populate_indicators_1h(self, dataframe: DataFrame, metadata: dict) -> DataFrame:

        L_UTBot_trend_alert_self_1h = UTBot_trend(dataframe=dataframe, key_value=self.key_value_l.value, atr_period=self.atr_period_l.value, ema_period=self.ema_period_l.value)
        dataframe['UT_Trend_L'] = L_UTBot_trend_alert_self_1h['trend']


        dataframe['ema'] = ta.EMA(dataframe['close'], timeperiod=100)
        dataframe['ema_22'] = ta.EMA(dataframe['close'], timeperiod=22)
        dataframe['rsi'] =  ta.RSI(dataframe, timeperiod=8)
        # Stochastic Fast
        stoch_fast = ta.STOCHF(dataframe)
        dataframe['fastd'] = stoch_fast['fastd']
        dataframe['fastk'] = stoch_fast['fastk']
        dataframe['adx'] = ta.ADX(dataframe)

        return dataframe


    def analyze_price_movements(self, dataframe, metadata, window=50):
        pair = metadata['pair']
        low = dataframe['low'].rolling(window=window).min()
        high = dataframe['high'].rolling(window=window).max()
        current_price = dataframe['close'].iloc[-1]
        mid_price = (low + high) / 2
        price_to_mid_ratio = ((current_price - mid_price) / (high - mid_price)).iloc[-1]

        self.pairs_close_to_high = list(set(self.pairs_close_to_high))
        # self.pairs_close_to_low = list(set(self.pairs_close_to_low))

        if price_to_mid_ratio > 0.5:
            if pair not in self.pairs_close_to_high:
                logging.info(f"added to Close_to_high bcoz is above 0.5 from mid ratio {pair}")
                self.pairs_close_to_high.append(pair)
                if pair in self.locked:
                    logging.info(f"Locking removed bcoz is in 0.5 from mid ratio and added to close_to_high {pair}")
                    self.unlock_pair(pair)
                    self.locked.remove(pair)
                    self.pairs_close_to_high.append(pair)
            if pair in self.pairs_close_to_high:
                if pair in self.locked:
                    logging.info(f"Locking removed bcoz is in 0.5 from mid ratio and is already in close_to_high {pair}")
                    self.unlock_pair(pair)
                    self.locked.remove(pair)

        else:
            if pair not in self.pairs_close_to_high:
                logging.info(f"Locked for 15m as is not in 0.5 from mid ratio {pair}")
                self.lock_pair(pair, until=datetime.now(timezone.utc) + timedelta(minutes=15))
                self.locked.append(pair)
            if pair in self.pairs_close_to_high:
                logging.info(f"Locked for 15m and also removed from close_to_high as is not in 0.5 from mid ratio {pair}")
                self.pairs_close_to_high.remove(pair)
                self.lock_pair(pair, until=datetime.now(timezone.utc) + timedelta(minutes=15))
                self.locked.append(pair)
                # if pair in self.locked:
                #     logging.info(f"removed from close_to_high as is not in 0.5 from mid ratio {pair}")
                #     self.pairs_close_to_high.remove(pair)

        user_data_directory = os.path.join('user_data')
        if not os.path.exists(user_data_directory):
            os.makedirs(user_data_directory)
        with open(os.path.join(user_data_directory, 'high_moving_pairs.json'), 'w') as f:
            json.dump(self.pairs_close_to_high, f, indent=4)


    def save_dictionaries_to_disk(self):
        try:
            user_data_directory = os.path.join('user_data')
            if not os.path.exists(user_data_directory):
                os.makedirs(user_data_directory)
            with open(os.path.join(user_data_directory, 'lowest_prices.json'), 'w') as file:
                json.dump(self.lowest_prices, file, indent=4)
            with open(os.path.join(user_data_directory, 'highest_prices.json'), 'w') as file:
                json.dump(self.highest_prices, file, indent=4)
            with open(os.path.join(user_data_directory, 'price_drop_percentage.json'), 'w') as file:
                json.dump(self.price_drop_percentage, file, indent=4)
        except Exception as ex:
            logging.error(str(ex))
            pass
    

    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        if not self.dp:
            # Don't do anything if DataProvider is not available.
            return dataframe
        
        
        
        L_UTBot_trend_alert = UTBot_trend(dataframe=dataframe, key_value=self.key_value_l.value, atr_period=self.atr_period_l.value, ema_period=self.ema_period_l.value)
        dataframe['UT_Trend_L'] = L_UTBot_trend_alert['trend']

        L_ewo_signal_alert = ewo_signal(data=dataframe, src='close', sma1length=self.sma1length_l.value, sma2length=self.sma2length_l.value, use_percent=True)
        dataframe['EWO_Trend_L'] = L_ewo_signal_alert['trend']

        L_stoch_rsi_alert = stoch_rsi(dataframe=dataframe, smoothK=3, smoothD=3, lengthRSI=self.lengthRSI_l.value, lengthStoch=14, src='close')
        dataframe['stoch_rsi_kline_L'] = L_stoch_rsi_alert['k']
        dataframe['stoch_rsi_dline_L'] = L_stoch_rsi_alert['d']

        # ADX
        dataframe['adx'] = ta.ADX(dataframe)

        # ATR
        dataframe['atr'] = ta.ATR(dataframe['high'], dataframe['low'], dataframe['close'], timeperiod=20)

        
        dataframe['ema_100'] = ta.EMA(dataframe['close'], timeperiod=100)


        # Volume Weighted
        dataframe['volume_mean'] = dataframe['volume'].rolling(self.volume_check.value).mean().shift(1)

        dataframe = self.pump_dump_protection(dataframe, metadata)


        return dataframe

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:

        
        self.analyze_price_movements(dataframe=dataframe, metadata=metadata, window=100)
        better_pair_long = metadata['pair'] in self.pairs_close_to_high
        # better_pair_short = metadata['pair'] not in self.pairs_close_to_low
        
        conditions_long = []
        conditions_short = []
        dataframe.loc[:, 'enter_tag'] = ''

        buy_1 = (
                
                        (dataframe['adx'] > self.adx_long_min.value) & # trend strength confirmation

                        (dataframe['UT_Trend_L'] == 1) &
                        (dataframe['close'] > dataframe['open']) &
                        # (dataframe['close'].shift(1) > dataframe['open'].shift(1)) &
                        (dataframe['ema_100'] < dataframe['close']) &
                        (dataframe['ema_100'].shift(20) < dataframe['ema_100']) &
                        (dataframe['ema_100'].shift(15) < dataframe['ema_100']) &
                        (dataframe['ema_100'].shift(10) < dataframe['ema_100']) &
                        (dataframe['ema_100'].shift(5) < dataframe['ema_100']) &
                        (dataframe['ema_100'].shift(2) < dataframe['ema_100']) &

                        ((dataframe['EWO_Trend_L'] < 0) &
                        (dataframe['stoch_rsi_kline_L']  > dataframe['stoch_rsi_dline_L'] ) &
                        ((dataframe['stoch_rsi_kline_L'] > 70) |
                        ((dataframe['stoch_rsi_kline_L'] < 50) & (dataframe['stoch_rsi_kline_L'] > 30))) ) |

                        ((dataframe['EWO_Trend_L'] > 0) &
                        (dataframe['stoch_rsi_kline_L']  > dataframe['stoch_rsi_dline_L'] ) &
                        (dataframe['stoch_rsi_kline_L'] < 70) &
                        (dataframe['stoch_rsi_kline_L'] > 50) ) &
                        

                            (dataframe['ema_15m'] < dataframe['close']) & 
                            (dataframe['fastk_15m'] > dataframe['fastd_15m']) &
                            (dataframe['rsi_15m'] > 50) &

                            (dataframe['ema_1h'] < dataframe['close']) & 
                            (dataframe['fastk_1h'] > dataframe['fastd_1h']) &
                            (dataframe['rsi_1h'] > 50) &


                            (dataframe['btc_ema_1h'] < dataframe['btc_closeBTC_1h']) &
                            (dataframe['btc_fastk_1h'] > dataframe['btc_fastd_1h']) &
                            # (dataframe['btc_rsi_1h'] > 55) & 

                            ((dataframe['close'] - dataframe['open']) > (2*(dataframe["open"] - dataframe['low']))) &
                            ((dataframe["open"] - dataframe['low']) > (2*(dataframe["high"] - dataframe['close']))) &

                            (dataframe['volume'] > dataframe['volume_mean']) &
                            (dataframe['pnd_volume_warn'] == 1) &
                            (dataframe['volume'] > 0)
        )


        # dataframe.loc[buy_1, 'enter_tag'] += 'buy_1 '
        # conditions_long.append(buy_1)

        # if conditions_long:
        #     dataframe.loc[
        #         reduce(lambda x, y: x | y, conditions_long),
        #          'enter_long'] = 1
            
        dataframe.loc[buy_1, 'enter_tag'] += 'buy_1 '
        conditions_long.append(buy_1)

        # if conditions_long:
        #     dataframe.loc[
        #         reduce(lambda x, y: x | y, conditions_long) & better_pair_long,
        #          'enter_long'] = 1
            
        if conditions_long:
            dataframe.loc[
                reduce(lambda x, y: x | y, conditions_long) & better_pair_long,
                 'enter_long'] = 1
            
        # Print the Analyzed pair
        # print(f"result for {metadata['pair']}")

        # Inspect the last 5 rows
        # print(better_pair_long)
        # print(dataframe.tail())
        
        return dataframe

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:


        dataframe.loc[(
                        # (dataframe['btc_ema_22_1h'] > dataframe['btc_closeBTC_1h']) &
                        # (dataframe['UT_Trend_L_1h'] < 0) &
                        (dataframe['btc_ema_1h'] > dataframe['btc_closeBTC_1h']) &
                        (dataframe['btc_ema_22_1h'] > dataframe['btc_closeBTC_1h']) &
                        (dataframe['btc_ema_1h'] > dataframe['btc_ema_22_1h']) &
                        (dataframe['ema_15m'] > dataframe['close_15m'])
                        # (dataframe['btc_UT_Trend_L_1h'] < 0) &
                        # (dataframe['btc_UT_Trend_L_15m'] < 0) &
                        # (dataframe['eth_UT_Trend_L_1h'] < 0) &
                        # (dataframe['eth_UT_Trend_L_15m'] < 0) 
                    

        ), 'exit_long'] = 1
        dataframe.loc[(), 'exit_short'] = 0
            
        return dataframe
    
    
    # def custom_exit(self, pair: str, trade: 'Trade', current_time: 'datetime', current_rate: float, current_profit: float, **kwargs) -> str:
    #     dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
    #     # dataframe = self.calculate_std(dataframe)
    #     last_candle = dataframe.iloc[-1].squeeze()
    #     last_candle_2 = dataframe.iloc[-2].squeeze()

    #     # self.analyze_price_movements(dataframe=dataframe, metadata=metadata, window=100)
    #     # better_pair_long_not = metadata['pair'] not in self.pairs_close_to_high

    #     # Number of candles to consider for mean and std calculation
    #     n_candles = 30

    #     # Calculate the mean and standard deviation for the last n candles
    #     mean_price = dataframe['close'].iloc[-n_candles:].mean()
    #     std_dev = dataframe['close'].iloc[-n_candles:].std()

    #     if (len(dataframe) > 1):
    #         previous_candle_1 = dataframe.iloc[-2].squeeze()

    #     enter_tag = 'empty'
    #     if hasattr(trade, 'enter_tag') and trade.enter_tag is not None:
    #         enter_tag = trade.enter_tag
    #     enter_tags = enter_tag.split()

    #     timeframe_minutes = timeframe_to_minutes(self.timeframe)
        
    #     if current_time - timedelta(minutes=int(timeframe_minutes)) > trade.open_date_utc:
            
    #         if any(c in ['enter_long_trade', 'buy_1', 'buy_1 ', 'buy_1 enter_long_trade'] for c in enter_tags):

    #             take_profit_long = ((last_candle['high']/trade.open_rate)- 1)
    #             # Calculate the upper and lower bounds using the standard deviation
    #             upper_bound = trade.open_rate + (std_dev* self.long_mul_tp.value)
    #             lower_bound = trade.open_rate - (std_dev * self.long_mul_sl.value)
    #             # if better_pair_long_not:
    #             #     if self.lock_pair(metadata['pair'], until=datetime.now(timezone.utc) + timedelta(minutes=45)):
    #             #         return f"sell_old_trade ({enter_tag}) "
    #             # if last_candle['close'] <= lower_bound:
    #             #     return f"sell_old_trade ({enter_tag}) "
    #             if last_candle['close'] >= upper_bound:
    #                 return f"take_profit ({enter_tag}) "
                

    #     return None # No sell signal by default
    
    def leverage(self, pair: str, current_time: datetime, current_rate: float,
                 proposed_leverage: float, max_leverage: float, side: str,
                 **kwargs) -> float:

        return self.leverage_num.value
    
    def confirm_trade_exit(self, pair: str, trade: Trade, order_type: str, amount: float,
                           rate: float, time_in_force: str, sell_reason: str,
                           current_time: datetime, **kwargs) -> bool:

        return True
    