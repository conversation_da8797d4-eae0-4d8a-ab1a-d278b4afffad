from freqtrade.strategy import IStrategy, DecimalParameter, IntParameter, BooleanParameter
from datetime import datetime
import pandas as pd
import numpy as np
import talib.abstract as ta
from technical import qtpylib
from typing import Optional

class AggressiveProfitStrategy(IStrategy):
    INTERFACE_VERSION = 3

    timeframe = '15m'  # Changed to 15m as per your request

    # Optimizable parameters for hyperopt
    # RSI parameters
    rsi_period = IntParameter(10, 20, default=14, space="buy")
    rsi_overbought = IntParameter(65, 80, default=70, space="sell")
    rsi_oversold = IntParameter(20, 35, default=30, space="buy")

    # MACD parameters
    macd_fast = IntParameter(8, 16, default=12, space="buy")
    macd_slow = IntParameter(20, 30, default=26, space="buy")
    macd_signal = IntParameter(7, 12, default=9, space="buy")

    # EMA parameters
    ema_short = IntParameter(15, 25, default=21, space="buy")
    ema_medium = IntParameter(45, 55, default=50, space="buy")
    ema_long = IntParameter(180, 220, default=200, space="buy")

    # Bollinger Bands parameters
    bb_period = IntParameter(15, 25, default=20, space="buy")
    bb_std = DecimalParameter(1.8, 2.5, default=2.0, space="buy")

    # Stochastic parameters
    stoch_k = IntParameter(10, 18, default=14, space="buy")
    stoch_d = IntParameter(3, 7, default=3, space="buy")

    # ADX parameters
    adx_period = IntParameter(10, 20, default=14, space="buy")
    adx_threshold = IntParameter(20, 30, default=25, space="buy")

    # Volume parameters
    volume_factor = DecimalParameter(1.2, 2.5, default=1.5, space="buy")

    # AO (Awesome Oscillator) parameters - ORIGINAL STRATEGY
    ao_enable = BooleanParameter(default=True, space="buy")
    ao_fast_period = IntParameter(3, 8, default=5, space="buy")
    ao_slow_period = IntParameter(25, 40, default=34, space="buy")
    ao_weight = DecimalParameter(0.5, 2.0, default=1.0, space="buy")

    # Risk management parameters
    use_dynamic_sl = BooleanParameter(default=True, space="protection")
    atr_multiplier = DecimalParameter(1.5, 3.0, default=2.0, space="protection")

    # Use market orders for both entry and exit
    order_types = {
        'entry': 'market',
        'exit': 'market',
        'emergency_exit': 'market',
        'force_exit': 'market',
        'stoploss': 'market',
        'stoploss_on_exchange': False
    }

    # AGGRESSIVE ROI for 100%+ profits
    minimal_roi = {
        "0": 10.0,    # 1000% max profit - let winners run!
        "60": 5.0,    # 500% after 1 hour
        "120": 2.0,   # 200% after 2 hours
        "240": 1.0,   # 100% after 4 hours
        "480": 0.5,   # 50% after 8 hours
        "720": 0.2    # 20% after 12 hours
    }
    stoploss = -0.15  # 15% stop loss - wider for bigger moves
    startup_candle_count: int = 250

    can_short = True  # Enable SHORT entries
    position_adjustment_enable = True  # Enable position scaling
    max_entry_position_adjustment = 3  # Allow up to 3 additional entries

    # Aggressive trailing stop - let profits run longer
    trailing_stop = True
    trailing_stop_positive = 0.05  # Start trailing at 5% profit
    trailing_stop_positive_offset = 0.15  # Trail by 15%
    trailing_only_offset_is_reached = True

    def leverage(self, pair: str, current_time: datetime, current_rate: float,
                 proposed_leverage: float, max_leverage: float, entry_tag: str | None, side: str,
                 **kwargs) -> float:
        max_leverage = 5
        return max_leverage

    def populate_indicators(self, dataframe: pd.DataFrame, metadata: dict) -> pd.DataFrame:
        """
        Comprehensive multi-indicator analysis combining trend, momentum, volatility, and volume
        """

        # Trend Analysis - Multiple EMAs
        dataframe['ema_short'] = ta.EMA(dataframe, timeperiod=self.ema_short.value)
        dataframe['ema_medium'] = ta.EMA(dataframe, timeperiod=self.ema_medium.value)
        dataframe['ema_long'] = ta.EMA(dataframe, timeperiod=self.ema_long.value)

        # Momentum Indicators
        # RSI
        dataframe['rsi'] = ta.RSI(dataframe, timeperiod=self.rsi_period.value)

        # MACD
        macd = ta.MACD(dataframe,
                      fastperiod=self.macd_fast.value,
                      slowperiod=self.macd_slow.value,
                      signalperiod=self.macd_signal.value)
        dataframe['macd'] = macd['macd']
        dataframe['macd_signal'] = macd['macdsignal']
        dataframe['macd_hist'] = macd['macdhist']

        # Stochastic Oscillator
        stoch = ta.STOCH(dataframe,
                        fastk_period=self.stoch_k.value,
                        slowk_period=self.stoch_d.value,
                        slowd_period=self.stoch_d.value)
        dataframe['stoch_k'] = stoch['slowk']
        dataframe['stoch_d'] = stoch['slowd']

        # ADX for trend strength
        dataframe['adx'] = ta.ADX(dataframe, timeperiod=self.adx_period.value)

        # Volatility Indicators
        # Bollinger Bands
        bollinger = qtpylib.bollinger_bands(dataframe['close'],
                                          window=self.bb_period.value,
                                          stds=self.bb_std.value)
        dataframe['bb_lower'] = bollinger['lower']
        dataframe['bb_middle'] = bollinger['mid']
        dataframe['bb_upper'] = bollinger['upper']
        dataframe['bb_width'] = (dataframe['bb_upper'] - dataframe['bb_lower']) / dataframe['bb_middle']
        dataframe['bb_percent'] = (dataframe['close'] - dataframe['bb_lower']) / (dataframe['bb_upper'] - dataframe['bb_lower'])

        # ATR for volatility and dynamic stop loss
        dataframe['atr'] = ta.ATR(dataframe, timeperiod=14)

        # Volume Analysis
        dataframe['volume_sma'] = dataframe['volume'].rolling(window=20).mean()
        dataframe['volume_ratio'] = dataframe['volume'] / dataframe['volume_sma']
        dataframe['obv'] = ta.OBV(dataframe)
        dataframe['obv_sma'] = dataframe['obv'].rolling(window=20).mean()

        # AWESOME OSCILLATOR (Original Strategy) - Enhanced
        median_price = (dataframe['high'] + dataframe['low']) / 2
        dataframe['ao_fast'] = median_price.rolling(window=self.ao_fast_period.value).mean()
        dataframe['ao_slow'] = median_price.rolling(window=self.ao_slow_period.value).mean()
        dataframe['ao'] = dataframe['ao_fast'] - dataframe['ao_slow']

        # AO additional signals
        dataframe['ao_cross_above'] = qtpylib.crossed_above(dataframe['ao'], 0)
        dataframe['ao_cross_below'] = qtpylib.crossed_below(dataframe['ao'], 0)
        dataframe['ao_positive'] = dataframe['ao'] > 0
        dataframe['ao_negative'] = dataframe['ao'] < 0
        dataframe['ao_increasing'] = dataframe['ao'] > dataframe['ao'].shift(1)
        dataframe['ao_decreasing'] = dataframe['ao'] < dataframe['ao'].shift(1)

        # AO momentum signals
        dataframe['ao_momentum_up'] = (
            (dataframe['ao'] > dataframe['ao'].shift(1)) &
            (dataframe['ao'].shift(1) > dataframe['ao'].shift(2))
        )
        dataframe['ao_momentum_down'] = (
            (dataframe['ao'] < dataframe['ao'].shift(1)) &
            (dataframe['ao'].shift(1) < dataframe['ao'].shift(2))
        )

        return dataframe

    def populate_entry_trend(self, dataframe: pd.DataFrame, metadata: dict) -> pd.DataFrame:
        """
        Multi-indicator entry logic combining trend, momentum, volatility, and volume confirmation
        """
        dataframe['enter_long'] = 0
        dataframe['enter_short'] = 0

        # LONG ENTRY CONDITIONS
        # Primary trend condition: EMAs aligned bullishly
        long_trend = (
            (dataframe['ema_short'] > dataframe['ema_medium']) &
            (dataframe['ema_medium'] > dataframe['ema_long']) &
            (dataframe['close'] > dataframe['ema_short'])
        )

        # Momentum conditions
        long_momentum = (
            # RSI oversold but recovering
            (dataframe['rsi'] > self.rsi_oversold.value) &
            (dataframe['rsi'] < 60) &
            (dataframe['rsi'] > dataframe['rsi'].shift(1)) |

            # MACD bullish
            (dataframe['macd'] > dataframe['macd_signal']) &
            (dataframe['macd_hist'] > dataframe['macd_hist'].shift(1)) |

            # Stochastic oversold and turning up
            (dataframe['stoch_k'] > dataframe['stoch_d']) &
            (dataframe['stoch_k'] > dataframe['stoch_k'].shift(1))
        )

        # Volatility conditions
        long_volatility = (
            # Price near lower BB but not oversold
            (dataframe['bb_percent'] > 0.1) &
            (dataframe['bb_percent'] < 0.4) &
            (dataframe['close'] > dataframe['bb_lower']) |

            # BB squeeze ending (expanding)
            (dataframe['bb_width'] > dataframe['bb_width'].shift(1))
        )

        # Volume confirmation
        long_volume = (
            (dataframe['volume_ratio'] > self.volume_factor.value) &
            (dataframe['obv'] > dataframe['obv_sma'])
        )

        # Trend strength
        long_strength = (dataframe['adx'] > self.adx_threshold.value)

        # AO SIGNALS (Original Strategy Enhanced)
        long_ao = (
            # Classic AO cross above zero
            (dataframe['ao_cross_above']) |

            # AO positive and increasing momentum
            (dataframe['ao_positive'] & dataframe['ao_momentum_up']) |

            # AO turning positive with momentum
            (dataframe['ao'] > 0) & (dataframe['ao_increasing']) &
            (dataframe['ao'].shift(1) <= 0)
        ) if self.ao_enable.value else False

        # AGGRESSIVE ENTRY: Need 3 out of 6 conditions (including AO)
        long_score = (
            long_trend.astype(int) +
            long_momentum.astype(int) +
            long_volatility.astype(int) +
            long_volume.astype(int) +
            long_strength.astype(int) +
            (long_ao.astype(int) * self.ao_weight.value if self.ao_enable.value else 0)
        )

        # Different entry tags based on signals
        dataframe.loc[
            (long_score >= 3) & long_ao,  # AO + other signals
            ['enter_long', 'enter_tag']
        ] = (1, 'ao_multi_long')

        dataframe.loc[
            (long_score >= 3) & ~long_ao,  # Multi-indicator without AO
            ['enter_long', 'enter_tag']
        ] = (1, 'multi_long')

        # SHORT ENTRY CONDITIONS
        # Primary trend condition: EMAs aligned bearishly
        short_trend = (
            (dataframe['ema_short'] < dataframe['ema_medium']) &
            (dataframe['ema_medium'] < dataframe['ema_long']) &
            (dataframe['close'] < dataframe['ema_short'])
        )

        # Momentum conditions
        short_momentum = (
            # RSI overbought but declining
            (dataframe['rsi'] < self.rsi_overbought.value) &
            (dataframe['rsi'] > 40) &
            (dataframe['rsi'] < dataframe['rsi'].shift(1)) |

            # MACD bearish
            (dataframe['macd'] < dataframe['macd_signal']) &
            (dataframe['macd_hist'] < dataframe['macd_hist'].shift(1)) |

            # Stochastic overbought and turning down
            (dataframe['stoch_k'] < dataframe['stoch_d']) &
            (dataframe['stoch_k'] < dataframe['stoch_k'].shift(1))
        )

        # Volatility conditions
        short_volatility = (
            # Price near upper BB but not overbought
            (dataframe['bb_percent'] < 0.9) &
            (dataframe['bb_percent'] > 0.6) &
            (dataframe['close'] < dataframe['bb_upper']) |

            # BB squeeze ending (expanding)
            (dataframe['bb_width'] > dataframe['bb_width'].shift(1))
        )

        # Volume confirmation
        short_volume = (
            (dataframe['volume_ratio'] > self.volume_factor.value) &
            (dataframe['obv'] < dataframe['obv_sma'])
        )

        # Trend strength
        short_strength = (dataframe['adx'] > self.adx_threshold.value)

        # AO SIGNALS for SHORT (Original Strategy Enhanced)
        short_ao = (
            # Classic AO cross below zero
            (dataframe['ao_cross_below']) |

            # AO negative and decreasing momentum
            (dataframe['ao_negative'] & dataframe['ao_momentum_down']) |

            # AO turning negative with momentum
            (dataframe['ao'] < 0) & (dataframe['ao_decreasing']) &
            (dataframe['ao'].shift(1) >= 0)
        ) if self.ao_enable.value else False

        # AGGRESSIVE ENTRY: Need 3 out of 6 conditions (including AO)
        short_score = (
            short_trend.astype(int) +
            short_momentum.astype(int) +
            short_volatility.astype(int) +
            short_volume.astype(int) +
            short_strength.astype(int) +
            (short_ao.astype(int) * self.ao_weight.value if self.ao_enable.value else 0)
        )

        # Different entry tags based on signals
        dataframe.loc[
            (short_score >= 3) & short_ao,  # AO + other signals
            ['enter_short', 'enter_tag']
        ] = (1, 'ao_multi_short')

        dataframe.loc[
            (short_score >= 3) & ~short_ao,  # Multi-indicator without AO
            ['enter_short', 'enter_tag']
        ] = (1, 'multi_short')

        return dataframe

    def populate_exit_trend(self, dataframe: pd.DataFrame, metadata: dict) -> pd.DataFrame:
        """
        AGGRESSIVE exit logic - only exit on strong reversal signals to let profits run
        """
        dataframe['exit_long'] = 0
        dataframe['exit_short'] = 0

        # LONG EXIT CONDITIONS - More restrictive to let profits run
        # Only exit on strong trend reversal
        long_exit_trend = (
            (dataframe['ema_short'] < dataframe['ema_medium']) &
            (dataframe['ema_medium'] < dataframe['ema_long']) &  # All EMAs must reverse
            (dataframe['close'] < dataframe['ema_long'])  # Price below long EMA
        )

        # Only exit on extreme momentum reversal
        long_exit_momentum = (
            # RSI extremely overbought AND declining
            (dataframe['rsi'] > 85) &  # Higher threshold
            (dataframe['rsi'] < dataframe['rsi'].shift(1)) &
            (dataframe['rsi'] < dataframe['rsi'].shift(2)) |  # 2 periods declining

            # Strong MACD bearish divergence
            (qtpylib.crossed_below(dataframe['macd'], dataframe['macd_signal'])) &
            (dataframe['macd'] < 0)  # MACD below zero
        )

        # Only exit on extreme volatility
        long_exit_volatility = (
            (dataframe['bb_percent'] > 0.98) &  # Very close to upper BB
            (dataframe['close'] > dataframe['bb_upper'] * 1.02)  # 2% above upper BB
        )

        # AO EXIT SIGNALS for LONG
        long_exit_ao = (
            # Classic AO cross below zero
            (dataframe['ao_cross_below']) |

            # AO negative with momentum down
            (dataframe['ao_negative'] & dataframe['ao_momentum_down']) |

            # Strong AO reversal
            (dataframe['ao'] < 0) & (dataframe['ao_decreasing']) &
            (dataframe['ao'].shift(1) > 0)
        ) if self.ao_enable.value else False

        # RESTRICTIVE EXIT: Need multiple strong signals
        long_exit_score = (
            long_exit_trend.astype(int) +
            long_exit_momentum.astype(int) +
            long_exit_volatility.astype(int) +
            (long_exit_ao.astype(int) * self.ao_weight.value if self.ao_enable.value else 0)
        )

        # Different exit tags based on signals
        dataframe.loc[
            (long_exit_score >= 2) & long_exit_ao,  # AO + other exit signals
            ['exit_long', 'exit_tag']
        ] = (1, 'ao_reversal_long')

        dataframe.loc[
            (long_exit_score >= 2) & ~long_exit_ao,  # Multi-indicator exit without AO
            ['exit_long', 'exit_tag']
        ] = (1, 'multi_exit_long')

        # SHORT EXIT CONDITIONS - More restrictive to let profits run
        # Only exit on strong trend reversal
        short_exit_trend = (
            (dataframe['ema_short'] > dataframe['ema_medium']) &
            (dataframe['ema_medium'] > dataframe['ema_long']) &  # All EMAs must reverse
            (dataframe['close'] > dataframe['ema_long'])  # Price above long EMA
        )

        # Only exit on extreme momentum reversal
        short_exit_momentum = (
            # RSI extremely oversold AND rising
            (dataframe['rsi'] < 15) &  # Lower threshold
            (dataframe['rsi'] > dataframe['rsi'].shift(1)) &
            (dataframe['rsi'] > dataframe['rsi'].shift(2)) |  # 2 periods rising

            # Strong MACD bullish divergence
            (qtpylib.crossed_above(dataframe['macd'], dataframe['macd_signal'])) &
            (dataframe['macd'] > 0)  # MACD above zero
        )

        # Only exit on extreme volatility
        short_exit_volatility = (
            (dataframe['bb_percent'] < 0.02) &  # Very close to lower BB
            (dataframe['close'] < dataframe['bb_lower'] * 0.98)  # 2% below lower BB
        )

        # AO EXIT SIGNALS for SHORT
        short_exit_ao = (
            # Classic AO cross above zero
            (dataframe['ao_cross_above']) |

            # AO positive with momentum up
            (dataframe['ao_positive'] & dataframe['ao_momentum_up']) |

            # Strong AO reversal
            (dataframe['ao'] > 0) & (dataframe['ao_increasing']) &
            (dataframe['ao'].shift(1) < 0)
        ) if self.ao_enable.value else False

        # RESTRICTIVE EXIT: Need multiple strong signals
        short_exit_score = (
            short_exit_trend.astype(int) +
            short_exit_momentum.astype(int) +
            short_exit_volatility.astype(int) +
            (short_exit_ao.astype(int) * self.ao_weight.value if self.ao_enable.value else 0)
        )

        # Different exit tags based on signals
        dataframe.loc[
            (short_exit_score >= 2) & short_exit_ao,  # AO + other exit signals
            ['exit_short', 'exit_tag']
        ] = (1, 'ao_reversal_short')

        dataframe.loc[
            (short_exit_score >= 2) & ~short_exit_ao,  # Multi-indicator exit without AO
            ['exit_short', 'exit_tag']
        ] = (1, 'multi_exit_short')

        return dataframe

    def adjust_trade_position(self, trade: 'Trade', current_time: datetime,
                            current_rate: float, current_profit: float,
                            min_stake: Optional[float], max_stake: float,
                            current_entry_rate: float, current_exit_rate: float,
                            current_entry_profit: float, current_exit_profit: float,
                            **kwargs) -> Optional[float]:
        """
        Aggressive position scaling - add to winning positions
        """
        # Only add to profitable positions
        if current_profit < 0.02:  # Less than 2% profit
            return None

        # Scale in at specific profit levels
        if trade.nr_of_successful_entries == 0 and current_profit > 0.05:  # 5% profit
            return max_stake * 0.5  # Add 50% of original position
        elif trade.nr_of_successful_entries == 1 and current_profit > 0.10:  # 10% profit
            return max_stake * 0.3  # Add 30% of original position
        elif trade.nr_of_successful_entries == 2 and current_profit > 0.20:  # 20% profit
            return max_stake * 0.2  # Add 20% of original position

        return None

    def custom_stoploss(self, pair: str, trade: 'Trade', current_time: datetime,
                       current_rate: float, current_profit: float, **kwargs) -> float:
        """
        Dynamic stop loss based on ATR
        """
        if not self.use_dynamic_sl.value:
            return self.stoploss

        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        last_candle = dataframe.iloc[-1].squeeze()

        if trade.is_short:
            # For short positions, stop loss is above entry
            dynamic_sl = (last_candle['atr'] * self.atr_multiplier.value) / trade.open_rate
        else:
            # For long positions, stop loss is below entry
            dynamic_sl = -(last_candle['atr'] * self.atr_multiplier.value) / trade.open_rate

        # Don't make stop loss worse than the fixed one
        if trade.is_short:
            return max(dynamic_sl, -abs(self.stoploss))
        else:
            return max(dynamic_sl, self.stoploss)
