{"max_open_trades": 4, "stake_currency": "USDT", "stake_amount": 200, "tradable_balance_ratio": 0.99, "fiat_display_currency": "USD", "dry_run": true, "dry_run_wallet": 1000, "cancel_open_orders_on_exit": false, "trading_mode": "futures", "margin_mode": "isolated", "unfilledtimeout": {"entry": 10, "exit": 10, "exit_timeout_count": 0, "unit": "minutes"}, "entry_pricing": {"price_side": "other", "use_order_book": true, "order_book_top": 1, "price_last_balance": 0.0, "check_depth_of_market": {"enabled": false, "bids_to_ask_delta": 1}}, "exit_pricing": {"price_side": "other", "use_order_book": true, "order_book_top": 1}, "exchange": {"name": "gateio", "key": "4dc6f80017cf031f57f1106e9f2d702a", "secret": "49397c7d95edb6e435f6a9134ceced7b25811df3658e17dc7020db27edae8759", "ccxt_config": {}, "ccxt_async_config": {}, "type": "perpetual", "pair_whitelist": ["ETH/USDT:USDT"], "pair_blacklist": []}, "pairlists": [{"method": "StaticPairList"}], "edge": {"enabled": false, "process_throttle_secs": 3600, "calculate_since_number_of_days": 14, "allowed_risk": 0.015, "stoploss_range_min": -0.05, "stoploss_range_max": -0.25, "stoploss_range_step": -0.01, "minimum_winrate": 0.7, "minimum_expectancy": 0.025, "min_trade_number": 10, "max_trade_duration_minute": 240, "remove_pumps": false}, "telegram": {"enabled": false, "token": "", "chat_id": "", "keyboard": [["/daily", "/stats", "/balance", "/profit"], ["/status table", "/performance", "/whitelist"], ["/reload_config", "/count", "/logs"]]}, "api_server": {"enabled": true, "listen_ip_address": "0.0.0.0", "listen_port": 8082, "verbosity": "error", "enable_openapi": false, "jwt_secret_key": "50afb5e35bd5696a187badb5537dfs2b26q23d5226143827bt108e1de793d92c", "CORS_origins": [], "username": "user", "password": "password"}, "bot_name": "useful_strategy_name", "initial_state": "running", "force_entry_enable": false, "internals": {"process_throttle_secs": 5}}