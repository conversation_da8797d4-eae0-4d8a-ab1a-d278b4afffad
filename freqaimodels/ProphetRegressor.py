import logging
import warnings
from typing import Any, Dict, <PERSON><PERSON>
import numpy as np
import pandas as pd
from pandas import DataFrame
import numpy.typing as npt

try:
    from prophet import Prophet
    from prophet.diagnostics import cross_validation, performance_metrics
    PROPHET_AVAILABLE = True
except ImportError:
    PROPHET_AVAILABLE = False
    Prophet = None

from freqtrade.freqai.base_models.BaseRegressionModel import BaseRegressionModel
from freqtrade.freqai.data_kitchen import FreqaiData<PERSON><PERSON><PERSON>

logger = logging.getLogger(__name__)

class ProphetRegressor(BaseRegressionModel):
    """
    Prophet-based time series forecasting model for FreqAI.
    
    This model uses Facebook's Prophet library to forecast price movements
    by capturing trends, seasonality, and holiday effects in cryptocurrency data.
    
    Configuration example:
    "model_training_parameters": {
        "changepoint_prior_scale": 0.05,
        "seasonality_prior_scale": 10.0,
        "holidays_prior_scale": 10.0,
        "seasonality_mode": "multiplicative",
        "growth": "linear",
        "daily_seasonality": True,
        "weekly_seasonality": True,
        "yearly_seasonality": False,
        "interval_width": 0.8,
        "mcmc_samples": 0
    }
    """

    def __init__(self, **kwargs) -> None:
        super().__init__(**kwargs)
        
        if not PROPHET_AVAILABLE:
            raise ImportError(
                "Prophet is not installed. Please install it with: pip install prophet"
            )
        
        # Prophet-specific parameters
        config = self.freqai_info.get("model_training_parameters", {})
        self.changepoint_prior_scale = config.get("changepoint_prior_scale", 0.05)
        self.seasonality_prior_scale = config.get("seasonality_prior_scale", 10.0)
        self.holidays_prior_scale = config.get("holidays_prior_scale", 10.0)
        self.seasonality_mode = config.get("seasonality_mode", "multiplicative")
        self.growth = config.get("growth", "linear")
        self.daily_seasonality = config.get("daily_seasonality", True)
        self.weekly_seasonality = config.get("weekly_seasonality", True)
        self.yearly_seasonality = config.get("yearly_seasonality", False)
        self.interval_width = config.get("interval_width", 0.8)
        self.mcmc_samples = config.get("mcmc_samples", 0)
        
        # Suppress Prophet warnings
        warnings.filterwarnings("ignore", category=FutureWarning)
        
    def fit(self, data_dictionary: Dict, dk: FreqaiDataKitchen, **kwargs) -> Any:
        """
        Train the Prophet model on the provided data.
        """
        
        # Prepare data for Prophet
        train_df = self._prepare_prophet_data(
            data_dictionary["train_features"], 
            data_dictionary["train_labels"]
        )
        
        # Initialize Prophet model
        self.model = Prophet(
            changepoint_prior_scale=self.changepoint_prior_scale,
            seasonality_prior_scale=self.seasonality_prior_scale,
            holidays_prior_scale=self.holidays_prior_scale,
            seasonality_mode=self.seasonality_mode,
            growth=self.growth,
            daily_seasonality=self.daily_seasonality,
            weekly_seasonality=self.weekly_seasonality,
            yearly_seasonality=self.yearly_seasonality,
            interval_width=self.interval_width,
            mcmc_samples=self.mcmc_samples
        )
        
        # Add custom seasonalities for crypto markets
        self.model.add_seasonality(
            name='hourly', period=24/24, fourier_order=8
        )
        
        # Add external regressors (technical indicators)
        feature_columns = data_dictionary["train_features"].columns
        for col in feature_columns:
            if col.startswith('%'):  # FreqAI feature columns
                try:
                    self.model.add_regressor(col)
                except Exception as e:
                    logger.warning(f"Could not add regressor {col}: {e}")
        
        # Fit the model
        with warnings.catch_warnings():
            warnings.simplefilter("ignore")
            self.model.fit(train_df)
        
        return self.model

    def predict(
        self, unfiltered_df: DataFrame, dk: FreqaiDataKitchen, **kwargs
    ) -> Tuple[DataFrame, npt.NDArray[np.int_]]:
        """
        Make predictions using the trained Prophet model.
        """
        
        # Filter and prepare features
        dk.find_features(unfiltered_df)
        filtered_df, _ = dk.filter_features(
            unfiltered_df, dk.training_features_list, training_filter=False
        )
        
        # Apply feature pipeline
        filtered_df, outliers, _ = dk.feature_pipeline.transform(
            filtered_df, outlier_check=True
        )
        
        # Prepare data for Prophet prediction
        future_df = self._prepare_prophet_prediction_data(filtered_df)
        
        # Make predictions
        with warnings.catch_warnings():
            warnings.simplefilter("ignore")
            forecast = self.model.predict(future_df)
        
        # Extract predictions
        predictions = forecast[['yhat']].values
        
        # Create prediction dataframe
        pred_df = DataFrame(predictions, columns=[f"&-{dk.label_list[0]}"])
        
        return pred_df, outliers

    def _prepare_prophet_data(self, features: DataFrame, labels: DataFrame) -> DataFrame:
        """
        Prepare data in Prophet's required format (ds, y, regressors).
        """
        # Create base dataframe with required Prophet columns
        prophet_df = pd.DataFrame()
        
        # Prophet requires 'ds' (datestamp) and 'y' (target) columns
        if 'date' in features.index.names:
            prophet_df['ds'] = features.index.get_level_values('date')
        elif 'date' in features.columns:
            prophet_df['ds'] = features['date']
        else:
            # Create synthetic datetime index
            prophet_df['ds'] = pd.date_range(
                start='2020-01-01', periods=len(features), freq='5T'
            )
        
        # Target variable
        prophet_df['y'] = labels.iloc[:, 0].values
        
        # Add external regressors (technical indicators)
        for col in features.columns:
            if col.startswith('%'):  # FreqAI feature columns
                prophet_df[col] = features[col].values
        
        # Remove any rows with NaN values
        prophet_df = prophet_df.dropna()
        
        return prophet_df

    def _prepare_prophet_prediction_data(self, features: DataFrame) -> DataFrame:
        """
        Prepare prediction data for Prophet.
        """
        future_df = pd.DataFrame()
        
        # Create datetime column
        if 'date' in features.index.names:
            future_df['ds'] = features.index.get_level_values('date')
        elif 'date' in features.columns:
            future_df['ds'] = features['date']
        else:
            # Create synthetic datetime index
            future_df['ds'] = pd.date_range(
                start='2020-01-01', periods=len(features), freq='5T'
            )
        
        # Add external regressors
        for col in features.columns:
            if col.startswith('%'):  # FreqAI feature columns
                future_df[col] = features[col].values
        
        # Remove any rows with NaN values
        future_df = future_df.dropna()
        
        return future_df
