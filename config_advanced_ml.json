{"max_open_trades": 5, "stake_currency": "USDT", "stake_amount": "unlimited", "tradable_balance_ratio": 0.99, "fiat_display_currency": "USD", "dry_run": true, "dry_run_wallet": 10000, "cancel_open_orders_on_exit": false, "trading_mode": "futures", "margin_mode": "isolated", "unfilledtimeout": {"entry": 10, "exit": 10, "exit_timeout_count": 0, "unit": "minutes"}, "entry_pricing": {"price_side": "same", "use_order_book": true, "order_book_top": 1, "price_last_balance": 0.0, "check_depth_of_market": {"enabled": false, "bids_to_ask_delta": 1}}, "exit_pricing": {"price_side": "same", "use_order_book": true, "order_book_top": 1}, "exchange": {"name": "binance", "key": "", "secret": "", "ccxt_config": {}, "ccxt_async_config": {}, "pair_whitelist": ["ETH/USDT:USDT"], "pair_blacklist": []}, "pairlists": [{"method": "StaticPairList"}], "edge": {"enabled": false, "process_throttle_secs": 3600, "calculate_since_number_of_days": 7, "allowed_risk": 0.01, "stoploss_range_min": -0.01, "stoploss_range_max": -0.1, "stoploss_range_step": -0.01, "minimum_winrate": 0.6, "minimum_expectancy": 0.2, "min_trade_number": 10, "max_trade_duration_minute": 1440, "remove_pumps": false}, "freqai": {"enabled": true, "identifier": "advanced_ml_ensemble", "train_period_days": 60, "backtest_period_days": 14, "live_retrain_hours": 6, "expiration_hours": 12, "purge_old_models": 3, "save_backtest_models": true, "write_metrics_to_disk": true, "activate_tensorboard": true, "feature_parameters": {"include_timeframes": ["5m", "15m", "1h"], "include_corr_pairlist": ["BTC/USDT:USDT", "ETH/USDT:USDT"], "label_period_candles": 24, "include_shifted_candles": 3, "DI_threshold": 0.9, "weight_factor": 0.8, "principal_component_analysis": false, "use_SVM_to_remove_outliers": true, "indicator_periods_candles": [10, 20, 50], "plot_feature_importances": 10, "noise_standard_deviation": 0.005, "buffer_train_data_candles": 50}, "data_split_parameters": {"test_size": 0.25, "random_state": 42, "shuffle": false}, "model_training_parameters": {"ensemble_method": "weighted_average", "lstm_weight": 0.4, "prophet_weight": 0.4, "garch_weight": 0.2, "use_meta_learner": true, "meta_learner": "linear_regression", "lstm_config": {"learning_rate": 0.0003, "trainer_kwargs": {"n_steps": null, "batch_size": 64, "n_epochs": 50}, "model_kwargs": {"num_lstm_layers": 3, "hidden_dim": 128, "window_size": 10, "dropout_percent": 0.3}}, "prophet_config": {"changepoint_prior_scale": 0.05, "seasonality_prior_scale": 10.0, "holidays_prior_scale": 10.0, "seasonality_mode": "multiplicative", "growth": "linear", "daily_seasonality": true, "weekly_seasonality": true, "yearly_seasonality": false, "interval_width": 0.8, "mcmc_samples": 0}, "garch_config": {"vol_model": "GARCH", "p": 1, "q": 1, "mean_model": "ARX", "lags": 1, "distribution": "normal", "rescale": true, "forecast_horizon": 1}}}, "bot_name": "AdvancedMLBot", "force_entry_enable": true, "initial_state": "running", "internals": {"process_throttle_secs": 5}, "telegram": {"enabled": false, "token": "", "chat_id": ""}, "api_server": {"enabled": false, "listen_ip_address": "127.0.0.1", "listen_port": 8080, "verbosity": "error", "enable_openapi": false, "jwt_secret_key": "somethingrandom", "CORS_origins": [], "username": "", "password": ""}, "db_url": "sqlite:///tradesv3.dryrun.sqlite", "user_data_dir": "user_data", "dataformat_ohlcv": "json", "dataformat_trades": "jsongz"}